package com.juneyaoair.thirdentity.response.checkin;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "HOOpenTimeResult")
@XmlAccessorType(XmlAccessType.FIELD)
public class HOOpenTimeResult {
	private  String tipCode ;
	private String  messageCause ;
	private List<HOOpenTimeResultBean> openTimeList;
	
	public String getTipCode() {
		return tipCode;
	}
	public void setTipCode(String tipCode) {
		this.tipCode = tipCode;
	}
	public String getMessageCause() {
		return messageCause;
	}
	public void setMessageCause(String messageCause) {
		this.messageCause = messageCause;
	}
	public List<HOOpenTimeResultBean> getOpenTimeList() {
		return openTimeList;
	}
	public void setOpenTimeList(List<HOOpenTimeResultBean> openTimeList) {
		this.openTimeList = openTimeList;
	}
	
	
	
	
	
}
