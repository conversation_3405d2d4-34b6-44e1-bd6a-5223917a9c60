package com.juneyaoair.thirdentity.response.order.refund.confirm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "TicketRefundResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtRefundResp {
	private String Version; //接口版本号10
	private String ChannelCode; //渠道用户号B2C,CC等
	private String UserNo; //渠道用户人员号分配给渠道用户的工作人员号
	private int RefundId; //退票信息ID
	private String RefundNo; //退票信息NO
	private String RefundApplyDatetime; //退票申请时间yyyy-MM-dd hh:mm:ss
	private Double RefundableAmountSum; //应退金额合计
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	private boolean HoChannel;// 改期原票是否是第三方渠道票
	private String ChangeFormerTicketNo;//改期订单原票号
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public int getRefundId() {
		return RefundId;
	}
	public void setRefundId(int refundId) {
		RefundId = refundId;
	}
	public String getRefundApplyDatetime() {
		return RefundApplyDatetime;
	}
	public void setRefundApplyDatetime(String refundApplyDatetime) {
		RefundApplyDatetime = refundApplyDatetime;
	}
	public Double getRefundableAmountSum() {
		return RefundableAmountSum;
	}
	public void setRefundableAmountSum(Double refundableAmountSum) {
		RefundableAmountSum = refundableAmountSum;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}

	public String getRefundNo() {
		return RefundNo;
	}

	public void setRefundNo(String refundNo) {
		RefundNo = refundNo;
	}

	public boolean isHoChannel() {
		return HoChannel;
	}

	public void setHoChannel(boolean hoChannel) {
		HoChannel = hoChannel;
	}

	public String getChangeFormerTicketNo() {
		return ChangeFormerTicketNo;
	}

	public void setChangeFormerTicketNo(String changeFormerTicketNo) {
		ChangeFormerTicketNo = changeFormerTicketNo;
	}
}