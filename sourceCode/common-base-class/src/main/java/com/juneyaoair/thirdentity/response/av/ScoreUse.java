package com.juneyaoair.thirdentity.response.av;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "ScoreUse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ScoreUseRuleID","UseScore","Deductibls","PricePaid","PriceValue","Remark"})
public class ScoreUse {
	
	private	String	ScoreUseRuleID	;	//	使用规则ID
	private	int	UseScore	;	//	扣减积分
	private	int	Deductibls	;	//	抵扣金额
	private	Double	PricePaid	;	//	实际支付票价
	private	Double	PriceValue	;	//	票价
	private	String	Remark	;	//	使用说明
	
	public String getScoreUseRuleID() {
		return ScoreUseRuleID;
	}
	public void setScoreUseRuleID(String scoreUseRuleID) {
		ScoreUseRuleID = scoreUseRuleID;
	}
	public int getUseScore() {
		return UseScore;
	}
	public void setUseScore(int useScore) {
		UseScore = useScore;
	}
	public int getDeductibls() {
		return Deductibls;
	}
	public void setDeductibls(int deductibls) {
		Deductibls = deductibls;
	}
	public Double getPricePaid() {
		return PricePaid;
	}
	public void setPricePaid(Double pricePaid) {
		PricePaid = pricePaid;
	}
	public Double getPriceValue() {
		return PriceValue;
	}
	public void setPriceValue(Double priceValue) {
		PriceValue = priceValue;
	}
	public String getRemark() {
		return Remark;
	}
	public void setRemark(String remark) {
		Remark = remark;
	}
	
	@Override
	public String toString() {
		return "ScoreUse [ScoreUseRuleID=" + ScoreUseRuleID + ", UseScore="
				+ UseScore + ", Deductibls=" + Deductibls + ", PricePaid="
				+ PricePaid + ", PriceValue=" + PriceValue + ", Remark="
				+ Remark + "]";
	}
	
}
