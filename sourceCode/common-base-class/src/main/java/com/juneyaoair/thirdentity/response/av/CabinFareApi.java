package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlRootElement(name = "CabinFareApi")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ID","CabinCode","CabinNumber","CabinClass","ProductPriceList"})
public class CabinFareApi {
	
	private	String	ID	;	//	ID	标识唯一一个舱位,由航段序号+航段机场序号+航班号序号+舱位序号 如:0_0_0_0
	private	String	CabinCode	;	//	舱位代码	F、C、Y或X
	private	String	CabinNumber	;	//	可利用座位情况	"数字1到9表示可预订座位数据，A表示有9个以上的可预订座位数，其它字母含义请参见航信订座手册"
	private	String	CabinClass	;	//	舱位等级	F-头等舱、C-公务舱、Y-经济舱
	private	ProductPrice[]	ProductPriceList	;	//	运价信息列表	该字段值在没有查询到匹配运价时会为空
	
	
	public String getID() {
		return ID;
	}
	public void setID(String iD) {
		ID = iD;
	}
	public String getCabinCode() {
		return CabinCode;
	}
	public void setCabinCode(String cabinCode) {
		CabinCode = cabinCode;
	}
	public String getCabinNumber() {
		return CabinNumber;
	}
	public void setCabinNumber(String cabinNumber) {
		CabinNumber = cabinNumber;
	}
	public String getCabinClass() {
		return CabinClass;
	}
	public void setCabinClass(String cabinClass) {
		CabinClass = cabinClass;
	}
	public ProductPrice[] getProductPriceList() {
		return ProductPriceList;
	}
	public void setProductPriceList(ProductPrice[] productPriceList) {
		ProductPriceList = productPriceList;
	}
	@Override
	public String toString() {
		return "CabinFareApi [ID=" + ID + ", CabinCode=" + CabinCode
				+ ", CabinNumber=" + CabinNumber + ", CabinClass=" + CabinClass
				+ ", ProductPriceList=" + Arrays.toString(ProductPriceList)
				+ "]";
	}
	
	
	

}
