package com.juneyaoair.thirdentity.request.reservation;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @ClassName PtQueryCancelPnrRecordInfoReq
 * <AUTHOR>
 * @Description
 * @Date 2021-08-03 16:09
 **/
@XmlRootElement(name = "PtQueryCancelPnrRecordInfoReq")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PtQueryCancelPnrRecordInfoReq {
    private String Version; // 接口版本号
    private String ChannelCode; // 渠道用户号
    private String UserNo; // 渠道工作人员号
    private String TicketNo; // 电子客票号
    private String FfCardNo; // 会员卡号
    private String Status; // 取消订座状态 0-未取消，1-已取消，2-失败
    private int Page;
    private int Size;


    public PtQueryCancelPnrRecordInfoReq(String version, String channelCode, String userNo, String ffCardNo, String status) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        FfCardNo = ffCardNo;
        Status = status;
    }
}
