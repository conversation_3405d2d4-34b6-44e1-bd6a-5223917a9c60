package com.juneyaoair.thirdentity.response.av;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "ScoreGift")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ScoreGiftRuleID","GiftPercentage","GiftScore","Remark"})
public class ScoreGift {
	
	private	String	ScoreGiftRuleID	;	//	赠送规则ID	
	private	int	GiftPercentage	;	//	赠送比例	计算结果进行四舍五入
	private	int	GiftScore	;	//	赠送积分	"在航班查询时为0，赠送需要根据实际支付价来计算，如果使用了积分需要扣除积分抵扣金额后再计算计算公式:四舍五入到个位((PriceValue * GiftPercentage) / 100)"
	private	String	Remark	;	//	赠送说明
	
	
	public String getScoreGiftRuleID() {
		return ScoreGiftRuleID;
	}
	public void setScoreGiftRuleID(String scoreGiftRuleID) {
		ScoreGiftRuleID = scoreGiftRuleID;
	}
	public int getGiftPercentage() {
		return GiftPercentage;
	}
	public void setGiftPercentage(int giftPercentage) {
		GiftPercentage = giftPercentage;
	}
	public int getGiftScore() {
		return GiftScore;
	}
	public void setGiftScore(int giftScore) {
		GiftScore = giftScore;
	}
	public String getRemark() {
		return Remark;
	}
	public void setRemark(String remark) {
		Remark = remark;
	}
	
	@Override
	public String toString() {
		return "ScoreGift [ScoreGiftRuleID=" + ScoreGiftRuleID
				+ ", GiftPercentage=" + GiftPercentage + ", GiftScore="
				+ GiftScore + ", Remark=" + Remark + "]";
	}
	
}
