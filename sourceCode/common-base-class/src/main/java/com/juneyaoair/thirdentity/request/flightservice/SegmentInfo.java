package com.juneyaoair.thirdentity.request.flightservice;

/**
 * Created by qinxiaoming on 2016-4-22.
 */
public class SegmentInfo {
    /// <summary>
    /// 旅行顺序	第一段为从0开始，第二段为1，依次增加
    /// </summary>
    private int SegNO;
    /// <summary>
    /// 航班日期 yyyy-mm-dd
    /// </summary>
    private String FlightDate;
    /// <summary>
    /// 起飞城市
    /// </summary>
    private String DepCity;
    /// <summary>
    /// 到达城市
    /// </summary>
    private String ArrCity;

    public SegmentInfo(int segNO, String depDateTime, String depAirport, String arrAirport) {
        SegNO = segNO;
        FlightDate = depDateTime;
        DepCity = depAirport;
        ArrCity = arrAirport;
    }

    public int getSegNO() {
        return SegNO;
    }

    public void setSegNO(int segNO) {
        SegNO = segNO;
    }

    public String getFlightDate() {
        return FlightDate;
    }

    public void setFlightDate(String flightDate) {
        FlightDate = flightDate;
    }

    public String getDepCity() {
        return DepCity;
    }

    public void setDepCity(String depCity) {
        DepCity = depCity;
    }

    public String getArrCity() {
        return ArrCity;
    }

    public void setArrCity(String arrCity) {
        ArrCity = arrCity;
    }
}
