package com.juneyaoair.thirdentity.upclass.response;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.upclass.request.TicketUpgradeCabinInfoDto;
import lombok.Data;

import java.util.List;

/**
 * 查询舱位信息返回结果
 */
@Data
public class TicketUpgradeCabinInfoResponse {

    @SerializedName("ResultCode")
    private String resultCode;
    @SerializedName("ErrorInfo")
    private String errorInfo;
    @SerializedName("TicketUpgradeCabinInfoDtos")
    private List<TicketUpgradeCabinInfoDto> ticketUpgradeCabinInfoDtos;
}
