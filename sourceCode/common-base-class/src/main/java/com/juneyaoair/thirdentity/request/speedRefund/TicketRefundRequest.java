package com.juneyaoair.thirdentity.request.speedRefund;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2017/1/16.
 * 客票快退请求类
 */
@XmlRootElement(name = "TicketRefundRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class TicketRefundRequest {
    private String Version;  //接口版本号
    private String ChannelCode;//渠道用户号  B2C,CC等
    private String UserNo;//渠道工作人员号     分配给渠道用户的工作人员号
    private String ChannelPayoutNo;//渠道退款编号     同一渠道不能有相同的渠道退款编号
    private String ChannelCustomerNo;//渠道客户编号   通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    private String OrderRequestIp;//预订人IP地址
    private String RouteType;//航程类型     单程：OW；往返：RT（RT时请注意飞行方向）
    private String InterFlag;//国内国际标识       国内：D；国际：I
    private String CurrencyCode;//币种代码  CNY人民币
    private String HandphoneNo;//手机号 购买保险必填
    private Double RefundAmount; //退款总金额
    private String PNR;
    private String BankAccountNO;//银行账户
    private String BankName;//银行名称
    private List<PtPassengerInfo> PassengerInfoList;//乘客信息列表
    private List<PtRefundSegmentInfo> RefundSegmentInfoList;//退票航段信息列表

    public TicketRefundRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getChannelPayoutNo() {
        return ChannelPayoutNo;
    }

    public void setChannelPayoutNo(String channelPayoutNo) {
        ChannelPayoutNo = channelPayoutNo;
    }

    public String getChannelCustomerNo() {
        return ChannelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        ChannelCustomerNo = channelCustomerNo;
    }

    public String getOrderRequestIp() {
        return OrderRequestIp;
    }

    public void setOrderRequestIp(String orderRequestIp) {
        OrderRequestIp = orderRequestIp;
    }

    public String getRouteType() {
        return RouteType;
    }

    public void setRouteType(String routeType) {
        RouteType = routeType;
    }

    public String getInterFlag() {
        return InterFlag;
    }

    public void setInterFlag(String interFlag) {
        InterFlag = interFlag;
    }

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        CurrencyCode = currencyCode;
    }

    public String getHandphoneNo() {
        return HandphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        HandphoneNo = handphoneNo;
    }

    public Double getRefundAmount() {
        return RefundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        RefundAmount = refundAmount;
    }

    public String getPNR() {
        return PNR;
    }

    public void setPNR(String PNR) {
        this.PNR = PNR;
    }

    public String getBankAccountNO() {
        return BankAccountNO;
    }

    public void setBankAccountNO(String bankAccountNO) {
        BankAccountNO = bankAccountNO;
    }

    public List<PtPassengerInfo> getPassengerInfoList() {
        return PassengerInfoList;
    }

    public void setPassengerInfoList(List<PtPassengerInfo> passengerInfoList) {
        PassengerInfoList = passengerInfoList;
    }

    public List<PtRefundSegmentInfo> getRefundSegmentInfoList() {
        return RefundSegmentInfoList;
    }

    public void setRefundSegmentInfoList(List<PtRefundSegmentInfo> refundSegmentInfoList) {
        RefundSegmentInfoList = refundSegmentInfoList;
    }

    public String getBankName() {
        return BankName;
    }

    public void setBankName(String bankName) {
        BankName = bankName;
    }
}
