package com.juneyaoair.thirdentity.callcenter.request;

import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国际客票退改服务
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PassengerServiceOrderRequest {
    /**
     * 旅客姓名
     */
    String passName;
    /**
     * 证件号码（护照号码）
     */
    String certNo;
    /**
     * 票号
     */
    String ticketNo;
    /**
     * 联系人姓名
     */
    String contactName;
    /**
     * 联系人号码
     */
    String contactTel;
    /**
     * 联系人邮箱
     */
    String contactEmail;
    /**
     * 服务类型（IRCS）
     */
    String serviceType;
    /**
     * 选择人工客服联系时间(上午A.M/下午P.M /晚上Evening)
     */
    String serviceTime;

    /**
     * 旅客诉求(改期Change/退票Refund)
     */
    String passAppeal;

    /**
     * 备注（用来填写其他）
     */
    String remark;


    /**
     * base
     */
    String userNo;
    /**
     * base
     */
    String Version;
    /**
     * base
     */
    String ChannelCode;


}

