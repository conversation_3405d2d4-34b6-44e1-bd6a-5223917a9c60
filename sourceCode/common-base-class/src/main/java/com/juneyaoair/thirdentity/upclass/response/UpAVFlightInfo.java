package com.juneyaoair.thirdentity.upclass.response;

import com.juneyaoair.thirdentity.response.av.FlightInfo;
import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.upclass.UpgradeCabinFare;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description  以AV查询的信息为主，升舱扩展部分字段
 * @date 2018/11/22  20:38.
 */
@Data
public class UpAVFlightInfo extends FlightInfo {
    private	int	SegNO;	//	旅行顺序	第一段为从0开始，第二段为1，依次增加
    private	String	FlightDirection;	//	飞行方向	去程为G,回程为B
    /**
     * 舱位运价信息
     */
    private List<UpgradeCabinFare> UpgradeCabinFareList;

    private Map<String,Fare> FareDic;
    private String DepCity;
    private String ArrCity;
    private String DepAirport;
    private String ArrAirport;
    private String FlightDate;

}
