package com.juneyaoair.thirdentity.response.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/** 我的众筹
 * Created by Administrator on 2017/10/30.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyActivityResponse {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<BuyActivityDetail> BuyActivityDetailList;
}
