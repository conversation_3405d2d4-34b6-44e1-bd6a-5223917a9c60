package com.juneyaoair.thirdentity.response.flightservice;

import java.util.List;

/**
 * Created by qinxiaoming on 2016-4-22.
 */
public class FlightServiceResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息
    private List<SegmentInfo> SegmentInfoList;

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public List<SegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<SegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }
}
