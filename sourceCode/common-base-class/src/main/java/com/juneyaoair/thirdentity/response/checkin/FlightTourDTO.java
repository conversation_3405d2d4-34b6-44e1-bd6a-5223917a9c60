package com.juneyaoair.thirdentity.response.checkin;


import com.juneyaoair.cuss.dto.booking.response.ENUM_SPECIAL_PSR;
import com.juneyaoair.cuss.dto.booking.response.flighttour.OrderInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 9:18 2018/11/19
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlightTourDTO {
    private String flightNo;//航班号
    private String operateCarrier;//销售方航班号
    private String segNo;//行程序号
    private String flightDate;//航班日期yyyy-MM-d
    private String depAirport;//出发机场三字码
    private String arrAirport; //到达机场三字码
    private String depTerminal;//出发机场航站楼
    private String arrTerminal;//到达机场航站楼
    private String etCode;//票号
    private String psrName;//旅客姓名
    private String psrEnName;//旅客英文名
    private String gender;//性别
    private String cabin;//舱位
    private String schDeptTime;//计划起飞时间HHmm
    private String expDeptTime;//预计到达时间HHmm
    private String boardingTime;//登机时间HHmm
    private String boardingGateNumber;//登机口号
    private String planeType;//机型
    /** 短信区域ID */
    private String areaId;
    private String phone;//值机手机号码
    private String asrSeatNo;//选座座位号
    private String checkInSeatNo;//值机座位号
    private String checkInDate;//值机成功时间
    private String checkCode;//值机验证码
    private String checkInChannel;//值机渠道
    private String chd;//儿童标识，如果是儿童，该字段为CHD字符串
    private boolean canCheckIn;//是否可值机
    private boolean canSelectSeat;//是否可选座
    /**
     * 值机功能是否关闭  true-代表关闭值机 其他正常
     */
    private Boolean closeCheckIn;
    private String pnrNo;
    /**
     * 计划到达日期.yyyy-MM-dd
     */
    private String schArrDate;


    /**
     * 计划到达时间
     */
    private String schArrTime;
    /**
     * 计划到达跨天天数
     */
    private Integer crossDay;
    /**
     *飞行时长,HHmm
     */
    private String duration;

    //订单信息
    private OrderInfoDTO orderInfo;

    //特殊旅客信息
    private List<ENUM_SPECIAL_PSR> specialPassenger;
    /**
     * 常旅客等级
     * */
    private String cardLevel;
    /**
     * 常旅客卡号
     * */
    private String cardId;
    /**
     * 常旅客航司
     * */
    private String cardAirline;

    /**
     * 选座结果ID
     * */
    private String selectSeatInfoId;

    /** 子订单号对应选座ID */
    private String couponCode;

    /** 渠道订单号 */
    private String channelOrderNo;

    /** 区域code */
    private String areaCode;

    /** 承运航司 */
    private String operationAirline;

    @ApiModelProperty(value = "市场方航班号")
    private String marketingFlightNo;
    @ApiModelProperty(value = "承运方航班号")
    private String operationFlightNo;
    @ApiModelProperty(value = "乘客类型")
    private String passengerType;
}
