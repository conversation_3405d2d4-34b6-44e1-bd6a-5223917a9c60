package com.juneyaoair.thirdentity.response.speedRefund;

/**
 * Created by yaocf on 2017/1/16.
 * 客票快退结果
 */
public class TicketRefundResponse {
    private String ChannelCode;//渠道用户号  B2C,CC等
    private String ChannelPayoutNo;//渠道退款编号  同一渠道不能有相同的渠道退款编号
    private String UserNo;//渠道工作人员号  分配给渠道用户的工作人员号
    private String ChannelCustomerNo;//渠道客户编号  通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    private String TicketPayoutNO;//客票退款编号
    private Double RefundAmount;//退款总金额
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getChannelPayoutNo() {
        return ChannelPayoutNo;
    }

    public void setChannelPayoutNo(String channelPayoutNo) {
        ChannelPayoutNo = channelPayoutNo;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getChannelCustomerNo() {
        return ChannelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        ChannelCustomerNo = channelCustomerNo;
    }

    public String getTicketPayoutNO() {
        return TicketPayoutNO;
    }

    public void setTicketPayoutNO(String ticketPayoutNO) {
        TicketPayoutNO = ticketPayoutNO;
    }

    public Double getRefundAmount() {
        return RefundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        RefundAmount = refundAmount;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }
}
