package com.juneyaoair.thirdentity.response.order.query;

import java.util.List;

/**
 * Created by yaocf on 2018/1/8.
 */
public class PtWeightProResp {
    private String Result;
    private String ChannelCustomerNo;
    private String ChannelOrderNo;
    private String OrderNo;
    private String OrderDatetime;
    private String PayState;
    private String OrderState;
    private List<BaggageBuy> BaggageBuyList;
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ResultCode;
    private String ErrorInfo;
    private Double baggageAmount;

    public PtWeightProResp() {
    }

    public String getResult() {
        return Result;
    }

    public void setResult(String result) {
        Result = result;
    }

    public String getChannelCustomerNo() {
        return ChannelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        ChannelCustomerNo = channelCustomerNo;
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getOrderDatetime() {
        return OrderDatetime;
    }

    public void setOrderDatetime(String orderDatetime) {
        OrderDatetime = orderDatetime;
    }

    public String getPayState() {
        return PayState;
    }

    public void setPayState(String payState) {
        PayState = payState;
    }

    public String getOrderState() {
        return OrderState;
    }

    public void setOrderState(String orderState) {
        OrderState = orderState;
    }

    public List<BaggageBuy> getBaggageBuyList() {
        return BaggageBuyList;
    }

    public void setBaggageBuyList(List<BaggageBuy> baggageBuyList) {
        BaggageBuyList = baggageBuyList;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public Double getBaggageAmount() {
        return baggageAmount;
    }

    public void setBaggageAmount(Double baggageAmount) {
        this.baggageAmount = baggageAmount;
    }
}
