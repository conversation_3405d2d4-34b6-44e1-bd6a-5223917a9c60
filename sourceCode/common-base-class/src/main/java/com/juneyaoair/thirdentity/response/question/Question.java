package com.juneyaoair.thirdentity.response.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/4/9  10:17.
 * 问卷问题
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Question {
    private Integer ID;//主键
    private String Title;//标题
    private Integer Category;//题目类型 1:单选 2:多选 3:其他
    private Integer RequiredOption;//是否必选项 1:非必选 2:必选
    private String Answers;//选择项用###分割,选择项有otherext时 表示有其他选择（可以按需求展示给用户输入或者选择）
    private String AnswersProportion;//选择项权重用###分割,选择项有otherext时 表示有其他选择（可以按需求展示给用户输入或者选择）
    private String UserAnswers;//用户选择答案
    private String UserOtherAnswer;//用户输入其他答案
    private AttachmentQuestion AttachmentQuestionEntity;//附加选项
}
