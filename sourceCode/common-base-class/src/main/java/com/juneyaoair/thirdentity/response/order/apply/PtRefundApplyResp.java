package com.juneyaoair.thirdentity.response.order.apply;

import com.juneyaoair.baseclass.request.booking.ChildrenOrder;
import com.juneyaoair.baseclass.request.booking.TicketOrderExt;
import com.juneyaoair.thirdentity.av.comm.PrivilegeDto;

import java.math.BigDecimal;
import java.util.List;

public class PtRefundApplyResp {
	private String Version; //接口版本号
	private String ChannelCode; //渠道用户号
	private String UserNo; //渠道工作人员号
	private String Currency; //原始货币代码
	private String PNR;//记录编号

	private Double Amount;
	private String TicketOrderNo; //机票订单编号
	private String RouteType; //航程类型	单程：OW；往返：RT
	private String InterFlag; //国际国内标识	D － 国内，I － 国际
	private String FareType;//联程/直达 枚举参考FareTypeEnum
	private String OrderSort;//订单类型
	private String Linker; //联系人
	private String LinkerTelphone; //联系人电话
	private String LinkerHandphone; //联系人手机
	private String LinkerEMail;
	private String PaidDatetime; //支付时间
	private String TicketRangeType;// 订单类型 MultiRange-多程

	private List<PtOrderPassengerInfo>	PassengerInfoList; //乘客信息列表
	private List<PtSegmentPriceInfo> SegmentInfoList; //航段信息列表
	private List<PtPassengerSegment> PassengerSegmentList; //人航段信息列表
	private List<PtOrderBriefInfo> OrderBriefInfoList;
	private List<PtOrderCouponDto> OrderCouponList;
	private BigDecimal PremiumReturnScore;// 额外返回积分

	private String FFPId;
	private String ResultCode; //结果代码
	private String ErrorInfo; //错误信息

	private boolean EnableRefund;//是否允许退票

	private BigDecimal DiscountAmountTotal;  //总优惠金额(拥军)


	/**
	 * 默认：空 WaitOrder-机票候补订单
	 */
	private String OrderType;


	private Boolean DisneyProdFlag;


	private String AdultTicketNo;

	private String AdultName;
	private List<ChildrenOrder> ChildrenOrderList;//儿童订单
    private List<PrivilegeDto> PrivilegeList;

	/**
	 * 是否可以使用后置积分
	 */
	private Boolean UseScores;

	private List<TicketOrderExt> TicketOrderExtList;


	public List<TicketOrderExt> getTicketOrderExtList() {
		return TicketOrderExtList;
	}

	public void setTicketOrderExtList(List<TicketOrderExt> ticketOrderExtList) {
		TicketOrderExtList = ticketOrderExtList;
	}

	public List<PrivilegeDto> getPrivilegeList() {
		return PrivilegeList;
	}

	public void setPrivilegeList(List<PrivilegeDto> privilegeList) {
		PrivilegeList = privilegeList;
	}

	public List<ChildrenOrder> getChildrenOrderList() {
		return ChildrenOrderList;
	}

	public void setChildrenOrderList(List<ChildrenOrder> childrenOrderList) {
		ChildrenOrderList = childrenOrderList;
	}

	public Boolean getDisneyProdFlag() {
		return DisneyProdFlag;
	}

	public void setDisneyProdFlag(Boolean disneyProdFlag) {
		DisneyProdFlag = disneyProdFlag;
	}

	public String getAdultTicketNo() {
		return AdultTicketNo;
	}

	public void setAdultTicketNo(String adultTicketNo) {
		AdultTicketNo = adultTicketNo;
	}

	public String getAdultName() {
		return AdultName;
	}

	public void setAdultName(String adultName) {
		AdultName = adultName;
	}

	public Boolean getUseScores() {
		return UseScores;
	}

	public void setUseScores(Boolean useScores) {
		UseScores = useScores;
	}

	public BigDecimal getDiscountAmountTotal() {
		return DiscountAmountTotal;
	}

	public void setDiscountAmountTotal(BigDecimal discountAmountTotal) {
		DiscountAmountTotal = discountAmountTotal;
	}

	public String getOrderType() {
		return OrderType;
	}

	public void setOrderType(String orderType) {
		OrderType = orderType;
	}

	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getCurrency() {
		return Currency;
	}
	public void setCurrency(String currency) {
		Currency = currency;
	}
	public String getTicketOrderNo() {
		return TicketOrderNo;
	}
	public void setTicketOrderNo(String ticketOrderNo) {
		TicketOrderNo = ticketOrderNo;
	}
	public String getRouteType() {
		return RouteType;
	}
	public void setRouteType(String routeType) {
		RouteType = routeType;
	}
	public String getInterFlag() {
		return InterFlag;
	}
	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}
	
	public List<PtOrderPassengerInfo> getPassengerInfoList() {
		return PassengerInfoList;
	}
	public void setPassengerInfoList(List<PtOrderPassengerInfo> passengerInfoList) {
		PassengerInfoList = passengerInfoList;
	}
	public List<PtSegmentPriceInfo> getSegmentInfoList() {
		return SegmentInfoList;
	}
	public void setSegmentInfoList(List<PtSegmentPriceInfo> segmentInfoList) {
		SegmentInfoList = segmentInfoList;
	}
	public List<PtPassengerSegment> getPassengerSegmentList() {
		return PassengerSegmentList;
	}
	public void setPassengerSegmentList(
			List<PtPassengerSegment> passengerSegmentList) {
		PassengerSegmentList = passengerSegmentList;
	}
	public String getLinker() {
		return Linker;
	}
	public void setLinker(String linker) {
		Linker = linker;
	}
	public String getLinkerTelphone() {
		return LinkerTelphone;
	}
	public void setLinkerTelphone(String linkerTelphone) {
		LinkerTelphone = linkerTelphone;
	}
	public String getLinkerHandphone() {
		return LinkerHandphone;
	}
	public void setLinkerHandphone(String linkerHandphone) {
		LinkerHandphone = linkerHandphone;
	}
	public String getPaidDatetime() {
		return PaidDatetime;
	}
	public void setPaidDatetime(String paidDatetime) {
		PaidDatetime = paidDatetime;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}
	public String getFFPId() {
		return FFPId;
	}
	public void setFFPId(String fFPId) {
		FFPId = fFPId;
	}

	public Double getAmount() {
		return Amount;
	}

	public void setAmount(Double amount) {
		Amount = amount;
	}

	public String getOrderSort() {
		return OrderSort;
	}

	public void setOrderSort(String orderSort) {
		OrderSort = orderSort;
	}

	public String getLinkerEMail() {
		return LinkerEMail;
	}

	public void setLinkerEMail(String linkerEMail) {
		LinkerEMail = linkerEMail;
	}

	public List<PtOrderBriefInfo> getOrderBriefInfoList() {
		return OrderBriefInfoList;
	}

	public void setOrderBriefInfoList(List<PtOrderBriefInfo> orderBriefInfoList) {
		OrderBriefInfoList = orderBriefInfoList;
	}
	public String getPNR() {
		return PNR;
	}

	public void setPNR(String PNR) {
		this.PNR = PNR;
	}

	public String getFareType() {
		return FareType;
	}

	public void setFareType(String fareType) {
		FareType = fareType;
	}

	public List<PtOrderCouponDto> getOrderCouponList() {
		return OrderCouponList;
	}

	public void setOrderCouponList(List<PtOrderCouponDto> orderCouponList) {
		OrderCouponList = orderCouponList;
	}

	public String getTicketRangeType() {
		return TicketRangeType;
	}

	public void setTicketRangeType(String ticketRangeType) {
		TicketRangeType = ticketRangeType;
	}

	public BigDecimal getPremiumReturnScore() {
		return PremiumReturnScore;
	}

	public void setPremiumReturnScore(BigDecimal premiumReturnScore) {
		PremiumReturnScore = premiumReturnScore;
	}

	public boolean isEnableRefund() {
		return EnableRefund;
	}

	public void setEnableRefund(boolean enableRefund) {
		EnableRefund = enableRefund;
	}
}