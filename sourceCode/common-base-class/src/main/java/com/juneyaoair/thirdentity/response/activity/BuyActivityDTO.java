package com.juneyaoair.thirdentity.response.activity;

import lombok.Getter;
import lombok.Setter;

/** 活动明细
 * Created by Administrator on 2017/10/28.
 */
@Getter
@Setter
public class BuyActivityDTO {
    private String ActivityCode;
    private String ActivityName;
    private String ActivityPrice;
    private String ActivityBuyPrice;
    private String ActivityStatus;
    private String ActivityStartDate;
    private String ActivityEndDate;
    private int ActivityNumber;
    private String WinnerId;
    private String WinnerName;
    private String Delflag;
    private int ActivityNo;
    private String Remark;
    private String ImgUrl;
    private int SpentTime;
    private int OrderNumber;
}
