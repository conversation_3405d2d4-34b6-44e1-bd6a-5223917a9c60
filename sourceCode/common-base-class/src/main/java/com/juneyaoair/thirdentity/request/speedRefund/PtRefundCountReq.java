package com.juneyaoair.thirdentity.request.speedRefund;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PtRefundCountReq
 * <AUTHOR>
 * @Description
 * @Date 2021-04-09 10:29
 **/
@Data
@NoArgsConstructor
public class PtRefundCountReq {
    private String Version; //接口版本号	10
    private String ChannelCode; //渠道用户号	B2C,CC等
    private String UserNo; //渠道工作人员号	分配给渠道用户的工作人员号
    private String CertNo; //证件号
    private String PassengerName; //旅客姓名

    public PtRefundCountReq(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }
}
