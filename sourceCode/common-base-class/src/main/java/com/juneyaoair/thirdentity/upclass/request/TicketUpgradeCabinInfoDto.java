package com.juneyaoair.thirdentity.upclass.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 查询指定航班舱位信息
 */
@Data
public class TicketUpgradeCabinInfoDto {

    @SerializedName("FlightNo")
    private String flightNo;//航班号
    @SerializedName("FlightDate")
    private String flightDate;//航班日期 yyyy-MM-dd
    @SerializedName("DepAirport")
    private String depAirport;//出发机场
    @SerializedName("DepDateTime")
    private String depDateTime;//出发时间 yyyy-MM-dd HH:mm
    @SerializedName("ArrDateTime")
    private String arrDateTime;//到达时间 yyyy-MM-dd HH:mm
    @SerializedName("ArrAirport")
    private String arrAirport;//到达机场
    @SerializedName("QueryCabin")
    private String queryCabin;//需要查询的舱位代码
    @SerializedName("DepCity")
    private String depCity;//出发城市
    @SerializedName("ArrCity")
    private String arrCity;//到达城市
    @SerializedName("CurrencyCode")
    private String currencyCode;//币种
}
