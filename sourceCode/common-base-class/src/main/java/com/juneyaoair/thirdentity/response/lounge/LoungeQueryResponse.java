package com.juneyaoair.thirdentity.response.lounge;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
@XmlRootElement(name = "LoungeQueryResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class LoungeQueryResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息
    private List<LoungeQuery> LoungeQueryList;

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public List<LoungeQuery> getLoungeQueryList() {
        return LoungeQueryList;
    }

    public void setLoungeQueryList(List<LoungeQuery> loungeQueryList) {
        LoungeQueryList = loungeQueryList;
    }
}
