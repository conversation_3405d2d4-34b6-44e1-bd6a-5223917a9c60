package com.juneyaoair.thirdentity.upclass;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 可升舱座位信息
 * @date 2018/11/14  18:54.
 */
@Data
public class UpgradeCabinFare {
    private String ID;
    private Double UpgradeFee; //升舱手续费
    private Double PriceDiff;//票面差额
    private String PriceShowType;
    private String CabinCode;
    private String CabinNumber;
    private String CabinClass;
    private String PriceRouteType;
    private String IntDiscount;
    private String Discount;
    /**
     * 票价(其中2位小数)
     */
    private Double PriceValue;
    /**
     * 销售参考价
     */
    private Double RSP;
    private Double YPrice;
    private String FareID;
    private String FareKey;
    private String YQTax;
    private String CNTax;
    private String CombineId;
    private String Cabins;
    private String CombineSegList;
    private String PassengerType;
}
