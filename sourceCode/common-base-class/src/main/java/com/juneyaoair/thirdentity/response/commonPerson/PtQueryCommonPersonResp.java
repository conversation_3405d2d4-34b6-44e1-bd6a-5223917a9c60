package com.juneyaoair.thirdentity.response.commonPerson;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PtQueryCommonPersonResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtQueryCommonPersonResp {
	private String Version; //接口版本号10
	private String ChannelCode; //渠道用户号B2C,CC等
	private String UserNo; //渠道工作人员号分配给渠道用户的工作人员号
	private String ChannelCustomerType; // 渠道客户类型,CRM - 常客,Other - 其它
	private String SortType; // 排序选项,LastBooking - 最近预订时间从近到远，BookingNum - 预订次数从大到小，Create － 创建时间从近到远
	private PtCommonPersonInfo[] CommonPersonInfoList; //常用旅客联系信息列表
	private int PageNo; //页码
	private int PageSize; //每页大小5~50条之间
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息

	public String getVersion(){
		return Version;
	}
	public void setVersion(String Version){
		this.Version=Version;
	}
	public String getChannelCode(){
		return ChannelCode;
	}
	public void setChannelCode(String ChannelCode){
		this.ChannelCode=ChannelCode;
	}
	public String getUserNo(){
		return UserNo;
	}
	public void setUserNo(String UserNo){
		this.UserNo=UserNo;
	}
	public String getChannelCustomerType() {
		return ChannelCustomerType;
	}
	public void setChannelCustomerType(String channelCustomerType) {
		ChannelCustomerType = channelCustomerType;
	}
	public String getSortType() {
		return SortType;
	}
	public void setSortType(String sortType) {
		SortType = sortType;
	}
	public PtCommonPersonInfo[] getCommonPersonInfoList() {
		return CommonPersonInfoList;
	}
	public void setCommonPersonInfoList(PtCommonPersonInfo[] commonPersonInfoList) {
		CommonPersonInfoList = commonPersonInfoList;
	}
	public int getPageNo(){
		return PageNo;
	}
	public void setPageNo(int PageNo){
		this.PageNo=PageNo;
	}
	public int getPageSize(){
		return PageSize;
	}
	public void setPageSize(int PageSize){
		this.PageSize=PageSize;
	}
	public String getResultCode(){
		return ResultCode;
	}
	public void setResultCode(String ResultCode){
		this.ResultCode=ResultCode;
	}
	public String getErrorInfo(){
		return ErrorInfo;
	}
	public void setErrorInfo(String ErrorInfo){
		this.ErrorInfo=ErrorInfo;
	}

}