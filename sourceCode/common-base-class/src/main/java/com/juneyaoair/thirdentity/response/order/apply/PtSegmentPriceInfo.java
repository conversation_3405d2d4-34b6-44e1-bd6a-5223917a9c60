package com.juneyaoair.thirdentity.response.order.apply;

import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.baseclass.av.common.TransferInfo;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.SegmentInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "SegmentPriceInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtSegmentPriceInfo {
	private int SegmentID; //航段ID	保存在统一订单数据库中的ID
	private String DepCityName;//起飞城市
	private String ArrCityName;/// 到达城市
	private String DepAirportName;/// 起飞机场
	private String ArrAirportName;/// 到达机场
	private Double UpgradeTicketPrice;

	private int SegNO; //旅行顺序	第一段为从0开始，第二段为1，依次增加
	private String FlightNo; //航班号
	private String DepDateTime; //航班起飞时间	yyyy-MM-dd HH:mm
	private String ArrDateTime; //航班到达时间	yyyy-MM-dd HH:mm
	private String DepCity; //起飞城市三字码
	private String ArrCity; //到达城市三字码
	private String DepAirport; //起飞机场三字码
	private String ArrAirport; //到达机场三字码
	private String Cabin; //舱位
	private String CabinClass; //舱位等级
	private boolean IsCodeShare	; //是否共享航班
	private String CarrierFlightNo; //承运航班号
	private String MealCode; //餐食代码
	private String MealName; //餐食名称
	private boolean IsSeatedOnPlane; //是否可以机上订位
	private String PlaneStyle; //机型
	private String DepTerm; //起飞航站楼
	private String ArrTerm; //到达航站楼
	private int StopNumber; //经停次数


	private String FlightDirection; //飞行方向	去程为G,回程为B
	private String TicketLaunchDatetime; //出票时的起飞时间	yyyy-MM-dd HH:mm
	private String SegmentStatus; //航段状态  C取消 D延误
	private String StatusReason;  //航段状态缘由

	/**
	 * 原机票子订单ID
	 * @return
	 */
	private int LaterPassengerSegmentId;

	/**
	 *原人航段Id
	 *
	 */
	private int FormerPassengerSegmentId;


	private boolean IsBuyInsurance; /// 是否购买保险
	private double InsuranceAmount;/// 保险金额
	private double PricePaid; /// 实际支付票价
	private double YQTax;/// 燃油费
	private double CNTax; /// 建设税
	private double Amount;/// 票面总额
	private double UseScore; /// 使用积分
	private double GiftScore;/// 赠送积分
	private String TicketState; /// 机票状态
	private String ID; /// 人航段标识
	private String ETicketNo; /// 票号
	private String TKTStatus;/// 客票状态
	private String FareID;/// 运价编号
	private int SegmentSeq;/// 票面航段序号
	private double Deduction;/// 退票手续费 打包运价不允许单独退时，退票手续费只有按退票人合计的退票手续费总金额
	private double UseFlowdPrice;/// 已使用航段票价 已使用航段票价和退票手续费两者只会有其一大于0
	private double RefundXTax;/// 应退税费 已使用段或退票规则规定不退税的不退
	private double RefundOther;/// 应退其它费用 已使用段或退票规则规定不退其它费用的不退
	private double XTax;/// 航段税费
	private double Other; /// 航段其它费用
	private String Comment;/// 备注项	用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private String Baggage;/// 行李重量
	private String ValidityPeriod;	/// 客票有效期	1Y1M1D
	private String MinStay;/// 最短停留时间	1Y1M1D
	private Boolean RefundedFlag;	/// 是否可退票
	private String RefundedComment;/// 退票政策描述 说明退票费收取或计算方法
	private String ChangedComment;/// 变更政策描述 说明改期、签转和升舱政策
	private boolean RescheduledFlag;/// 是否可免费改期
	private int FreeChangeTimes;/// 免费改期次数 免费改期不限次数时，目前运价系统会给出99次
	private boolean ChangeAirLineFlag;/// 是否可改签
	private boolean UpgradeFlag;	/// 是否可升舱
	private Double Deductibls;/// 积分抵用金额
	private Double CouponAmount;/// 优惠劵金额
	private Double UpgradeFee;/// 升舱手续费
	private Double GifiScore;/// 赠送积分
	private List<InsuranceInfo> InsuranceList;/// 18保险信息列表
	private SegmentInfo SegmentInfoOTO;/// 改期前航段信息

	private String TKTStatusName; /// 客票状态
	private String InsuranceAmountName;/// 保险金额
	private TransferInfo transferInfo;//航班中转信息

	public TransferInfo getTransferInfo() {
		return transferInfo;
	}

	public void setTransferInfo(TransferInfo transferInfo) {
		this.transferInfo = transferInfo;
	}

	public int getSegmentID() {
		return SegmentID;
	}
	public void setSegmentID(int segmentID) {
		SegmentID = segmentID;
	}
	public int getSegNO() {
		return SegNO;
	}
	public void setSegNO(int segNO) {
		SegNO = segNO;
	}
	public String getFlightDirection() {
		return FlightDirection;
	}
	public void setFlightDirection(String flightDirection) {
		FlightDirection = flightDirection;
	}
	public String getFlightNo() {
		return FlightNo;
	}
	public void setFlightNo(String flightNo) {
		FlightNo = flightNo;
	}
	public String getDepDateTime() {
		return DepDateTime;
	}
	public void setDepDateTime(String depDateTime) {
		DepDateTime = depDateTime;
	}
	public String getArrDateTime() {
		return ArrDateTime;
	}
	public void setArrDateTime(String arrDateTime) {
		ArrDateTime = arrDateTime;
	}
	public String getDepCity() {
		return DepCity;
	}
	public void setDepCity(String depCity) {
		DepCity = depCity;
	}
	public String getArrCity() {
		return ArrCity;
	}
	public void setArrCity(String arrCity) {
		ArrCity = arrCity;
	}
	public String getDepAirport() {
		return DepAirport;
	}
	public void setDepAirport(String depAirport) {
		DepAirport = depAirport;
	}
	public String getArrAirport() {
		return ArrAirport;
	}
	public void setArrAirport(String arrAirport) {
		ArrAirport = arrAirport;
	}
	public String getCabin() {
		return Cabin;
	}
	public void setCabin(String cabin) {
		Cabin = cabin;
	}
	public String getCabinClass() {
		return CabinClass;
	}
	public void setCabinClass(String cabinClass) {
		CabinClass = cabinClass;
	}
	public boolean isIsCodeShare() {
		return IsCodeShare;
	}
	public void setIsCodeShare(boolean isCodeShare) {
		IsCodeShare = isCodeShare;
	}
	public String getCarrierFlightNo() {
		return CarrierFlightNo;
	}
	public void setCarrierFlightNo(String carrierFlightNo) {
		CarrierFlightNo = carrierFlightNo;
	}
	public String getMealCode() {
		return MealCode;
	}
	public void setMealCode(String mealCode) {
		MealCode = mealCode;
	}
	public boolean isIsSeatedOnPlane() {
		return IsSeatedOnPlane;
	}
	public void setIsSeatedOnPlane(boolean isSeatedOnPlane) {
		IsSeatedOnPlane = isSeatedOnPlane;
	}
	public String getPlaneStyle() {
		return PlaneStyle;
	}
	public void setPlaneStyle(String planeStyle) {
		PlaneStyle = planeStyle;
	}
	public String getDepTerm() {
		return DepTerm;
	}
	public void setDepTerm(String depTerm) {
		DepTerm = depTerm;
	}
	public String getArrTerm() {
		return ArrTerm;
	}
	public void setArrTerm(String arrTerm) {
		ArrTerm = arrTerm;
	}
	public int getStopNumber() {
		return StopNumber;
	}
	public void setStopNumber(int stopNumber) {
		StopNumber = stopNumber;
	}
	public String getTicketLaunchDatetime() {
		return TicketLaunchDatetime;
	}
	public void setTicketLaunchDatetime(String ticketLaunchDatetime) {
		TicketLaunchDatetime = ticketLaunchDatetime;
	}
	public String getDepCityName() {
		return DepCityName;
	}
	public void setDepCityName(String depCityName) {
		DepCityName = depCityName;
	}
	public String getArrCityName() {
		return ArrCityName;
	}
	public void setArrCityName(String arrCityName) {
		ArrCityName = arrCityName;
	}
	public String getDepAirportName() {
		return DepAirportName;
	}
	public void setDepAirportName(String depAirportName) {
		DepAirportName = depAirportName;
	}
	public String getArrAirportName() {
		return ArrAirportName;
	}
	public void setArrAirportName(String arrAirportName) {
		ArrAirportName = arrAirportName;
	}
	public boolean isIsBuyInsurance() {
		return IsBuyInsurance;
	}
	public void setIsBuyInsurance(boolean isBuyInsurance) {
		IsBuyInsurance = isBuyInsurance;
	}
	public double getInsuranceAmount() {
		return InsuranceAmount;
	}
	public void setInsuranceAmount(double insuranceAmount) {
		InsuranceAmount = insuranceAmount;
	}
	public double getPricePaid() {
		return PricePaid;
	}
	public void setPricePaid(double pricePaid) {
		PricePaid = pricePaid;
	}
	public double getYQTax() {
		return YQTax;
	}
	public void setYQTax(double yQTax) {
		YQTax = yQTax;
	}
	public double getCNTax() {
		return CNTax;
	}
	public void setCNTax(double cNTax) {
		CNTax = cNTax;
	}
	public double getAmount() {
		return Amount;
	}
	public void setAmount(double amount) {
		Amount = amount;
	}
	public double getUseScore() {
		return UseScore;
	}
	public void setUseScore(double useScore) {
		UseScore = useScore;
	}
	public double getGiftScore() {
		return GiftScore;
	}
	public void setGiftScore(double giftScore) {
		GiftScore = giftScore;
	}
	public String getTicketState() {
		return TicketState;
	}
	public void setTicketState(String ticketState) {
		TicketState = ticketState;
	}
	public String getID() {
		return ID;
	}
	public void setID(String iD) {
		ID = iD;
	}
	public String getETicketNo() {
		return ETicketNo;
	}
	public void setETicketNo(String eTicketNo) {
		ETicketNo = eTicketNo;
	}
	public String getTKTStatus() {
		return TKTStatus;
	}
	public void setTKTStatus(String tKTStatus) {
		TKTStatus = tKTStatus;
	}
	public String getFareID() {
		return FareID;
	}
	public void setFareID(String fareID) {
		FareID = fareID;
	}
	public int getSegmentSeq() {
		return SegmentSeq;
	}
	public void setSegmentSeq(int segmentSeq) {
		SegmentSeq = segmentSeq;
	}
	public double getDeduction() {
		return Deduction;
	}
	public void setDeduction(double deduction) {
		Deduction = deduction;
	}
	public double getUseFlowdPrice() {
		return UseFlowdPrice;
	}
	public void setUseFlowdPrice(double useFlowdPrice) {
		UseFlowdPrice = useFlowdPrice;
	}
	public double getRefundXTax() {
		return RefundXTax;
	}
	public void setRefundXTax(double refundXTax) {
		RefundXTax = refundXTax;
	}
	public double getRefundOther() {
		return RefundOther;
	}
	public void setRefundOther(double refundOther) {
		RefundOther = refundOther;
	}
	public double getXTax() {
		return XTax;
	}
	public void setXTax(double xTax) {
		XTax = xTax;
	}
	public double getOther() {
		return Other;
	}
	public void setOther(double other) {
		Other = other;
	}
	public String getTKTStatusName() {
        if (CommonBaseConstants.OPEN_FOR_USE.equals(TKTStatus)) {
          return "正常使用";
	      }
	      if (CommonBaseConstants.REFUNDED.equals(TKTStatus)) {
	          return "已退票";
	      }
	      if (CommonBaseConstants.REFUND_APPLICATION.equals(TKTStatus)) {
	          return "已申请退票";
	      }
	      if (CommonBaseConstants.USED_FLOWN.equals(TKTStatus)) {
	          return "已使用";
	      }	
		return TKTStatus;
	}
	public void setTKTStatusName(String tKTStatusName) {
		TKTStatusName = tKTStatusName;
	}
	public String getInsuranceAmountName() {
		if (InsuranceAmount == 0)
        {
            return String.valueOf(InsuranceAmount);
        }
        return String.valueOf(InsuranceAmount)+(IsBuyInsurance ? "可退" : "不可退");
	}
	public void setInsuranceAmountName(String insuranceAmountName) {
		InsuranceAmountName = insuranceAmountName;
	}

	public Double getUpgradeTicketPrice() {
		return UpgradeTicketPrice;
	}

	public void setUpgradeTicketPrice(Double upgradeTicketPrice) {
		UpgradeTicketPrice = upgradeTicketPrice;
	}

	public boolean isCodeShare() {
		return IsCodeShare;
	}

	public void setCodeShare(boolean codeShare) {
		IsCodeShare = codeShare;
	}

	public boolean isSeatedOnPlane() {
		return IsSeatedOnPlane;
	}

	public void setSeatedOnPlane(boolean seatedOnPlane) {
		IsSeatedOnPlane = seatedOnPlane;
	}

	public boolean isBuyInsurance() {
		return IsBuyInsurance;
	}

	public void setBuyInsurance(boolean buyInsurance) {
		IsBuyInsurance = buyInsurance;
	}

	public String getComment() {
		return Comment;
	}

	public void setComment(String comment) {
		Comment = comment;
	}

	public String getBaggage() {
		return Baggage;
	}

	public void setBaggage(String baggage) {
		Baggage = baggage;
	}

	public String getValidityPeriod() {
		return ValidityPeriod;
	}

	public void setValidityPeriod(String validityPeriod) {
		ValidityPeriod = validityPeriod;
	}

	public String getMinStay() {
		return MinStay;
	}

	public void setMinStay(String minStay) {
		MinStay = minStay;
	}

	public Boolean getRefundedFlag() {
		return RefundedFlag;
	}

	public void setRefundedFlag(Boolean refundedFlag) {
		RefundedFlag = refundedFlag;
	}

	public String getRefundedComment() {
		return RefundedComment;
	}

	public void setRefundedComment(String refundedComment) {
		RefundedComment = refundedComment;
	}

	public String getChangedComment() {
		return ChangedComment;
	}

	public void setChangedComment(String changedComment) {
		ChangedComment = changedComment;
	}

	public Boolean getRescheduledFlag() {
		return RescheduledFlag;
	}

	public void setRescheduledFlag(Boolean rescheduledFlag) {
		RescheduledFlag = rescheduledFlag;
	}

	public int getFreeChangeTimes() {
		return FreeChangeTimes;
	}

	public void setFreeChangeTimes(int freeChangeTimes) {
		FreeChangeTimes = freeChangeTimes;
	}

	public Boolean getChangeAirLineFlag() {
		return ChangeAirLineFlag;
	}

	public void setChangeAirLineFlag(Boolean changeAirLineFlag) {
		ChangeAirLineFlag = changeAirLineFlag;
	}

	public Boolean getUpgradeFlag() {
		return UpgradeFlag;
	}

	public void setUpgradeFlag(Boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}

	public Double getDeductibls() {
		return Deductibls;
	}

	public void setDeductibls(Double deductibls) {
		Deductibls = deductibls;
	}

	public Double getCouponAmount() {
		return CouponAmount;
	}

	public void setCouponAmount(Double couponAmount) {
		CouponAmount = couponAmount;
	}

	public Double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public Double getGifiScore() {
		return GifiScore;
	}

	public void setGifiScore(Double gifiScore) {
		GifiScore = gifiScore;
	}

	public List<InsuranceInfo> getInsuranceList() {
		return InsuranceList;
	}

	public void setInsuranceList(List<InsuranceInfo> insuranceList) {
		InsuranceList = insuranceList;
	}

	public SegmentInfo getSegmentInfoOTO() {
		return SegmentInfoOTO;
	}

	public void setSegmentInfoOTO(SegmentInfo segmentInfoOTO) {
		SegmentInfoOTO = segmentInfoOTO;
	}

	public int getLaterPassengerSegmentId() {
		return LaterPassengerSegmentId;
	}

	public void setLaterPassengerSegmentId(int laterPassengerSegmentId) {
		LaterPassengerSegmentId = laterPassengerSegmentId;
	}

	public int getFormerPassengerSegmentId() {
		return FormerPassengerSegmentId;
	}

	public void setFormerPassengerSegmentId(int formerPassengerSegmentId) {
		FormerPassengerSegmentId = formerPassengerSegmentId;
	}

	public String getSegmentStatus() {
		return SegmentStatus;
	}

	public void setSegmentStatus(String segmentStatus) {
		SegmentStatus = segmentStatus;
	}

	public String getStatusReason() {
		return StatusReason;
	}

	public void setStatusReason(String statusReason) {
		StatusReason = statusReason;
	}

	public String getMealName() {
		return MealName;
	}

	public void setMealName(String mealName) {
		MealName = mealName;
	}
}