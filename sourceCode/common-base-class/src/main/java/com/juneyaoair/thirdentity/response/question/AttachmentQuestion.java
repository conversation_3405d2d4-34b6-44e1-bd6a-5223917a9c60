package com.juneyaoair.thirdentity.response.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/4/9  10:24.
 * 问卷附件项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentQuestion {
    private Integer ID;//主键
    private String Title;//标题
    private Integer Category;//题目类型 1:单选 2:多选
    private String Answers;//选择项用###分割，选择项有otherext时 表示有其他选择（可以按需求展示给用户输入或者选择）
    private String UserAnswer;//用户选择附加答案
    private String UserOtherAnswer;//用户输入附加答案
    private Integer RequiredOption;//是否必选项 1:非必选 2:必选
}
