package com.juneyaoair.thirdentity.response.reservation;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ListBean
 * <AUTHOR>
 * @Description
 * @Date 2021-08-06 15:09
 **/
@NoArgsConstructor
@Data
public class CancelOrderDTO {
    /**
     * CancelTicketSeatId : 54
     * TicketNo : 0181156013436
     * PName : 申梦月
     * Status : 1
     * CardNo : ******************
     * Pnr : NB6PMK
     * FfCardNo : 2881717786
     * FfpId : 3513951
     * CancelDatetime : 2021-08-06 14:27:48
     */

    private int CancelTicketSeatId;
    private String TicketNo;
    private String PName;
    private int Status;
    private String CardNo;
    private String Pnr;
    private String FfCardNo;
    private int FfpId;
    private String CancelDatetime;
}
