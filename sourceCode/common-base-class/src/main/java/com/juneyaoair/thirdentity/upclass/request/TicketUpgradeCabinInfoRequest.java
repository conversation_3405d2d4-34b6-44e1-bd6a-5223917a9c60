package com.juneyaoair.thirdentity.upclass.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 查询指定航班舱位信息
 */
@Data
public class TicketUpgradeCabinInfoRequest {

    @SerializedName("Version")
    private String version;// 版本号 20.0
    @SerializedName("ChannelCode")
    private String channelCode;//渠道用户号
    @SerializedName("UserNo")
    private String userNo;// 渠道用户
    @SerializedName("RandCode")
    private String randCode; //随机码，用于业务追踪
    @SerializedName("TicketUpgradeCabinInfos")
    private List<TicketUpgradeCabinInfoDto> ticketUpgradeCabinInfos;
    @SerializedName("LangCode")
    private String langCode;//语言编码
}
