package com.juneyaoair.thirdentity.response.order.apply;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName PtOrderCouponDto
 * @Description 统一订单优惠券信息
 * <AUTHOR>
 * @Date 2019/9/19 16:59
 **/
@Data
public class PtOrderCouponDto {

    private String CouponNo;//优惠券Code

    private String FfpId;//会员ID

    private String FfpCardNo;// 会员卡号

    private String CouponState;// 优惠券状态

    private String CouponRebate;// 折扣比例

    private BigDecimal CouponPrice;// 优惠金额

    private String CouponType;//优惠券类型 （领用类型）

    /**
     * 优惠券类型
     * @see com.juneyaoair.appenum.coupon.CouponTypeEnum
     */
    private String CouponUseType;

    /**
     * 优惠券类型
     * 畅飞卡2.0版本增加
     * @see com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum
     */
    private String CouponSource;

}
