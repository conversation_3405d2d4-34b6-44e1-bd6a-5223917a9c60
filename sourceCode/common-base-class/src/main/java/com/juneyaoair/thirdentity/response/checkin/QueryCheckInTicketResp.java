package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/7/29.
 */
@XmlRootElement(name = "QueryCheckInTicketResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryCheckInTicketResp {
    private String Result;
    private List<CheckInTicket> CheckInTicketList;
    private String ResultCode;
    private String ErrorInfo;

    public String getResult() {
        return Result;
    }

    public void setResult(String result) {
        Result = result;
    }

    public List<CheckInTicket> getCheckInTicketList() {
        return CheckInTicketList;
    }

    public void setCheckInTicketList(List<CheckInTicket> checkInTicketList) {
        CheckInTicketList = checkInTicketList;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }
}
