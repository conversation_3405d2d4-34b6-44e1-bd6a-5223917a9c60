package com.juneyaoair.thirdentity.response.speedRefund;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/7/2  16:43
 *@description: 直退客票详情
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class TicketPayoutDetailInfo {
    private String PassengerName; // 乘客姓名
    private String HandphoneNo; // 联系电话
    private String BankAccountNo; // 银行卡号
    private String PassengerType; // 旅客类型 如“ADT(成人)”
    private String TicketPayoutState;// 退票状态
    private String CreateAuditDate; // 退票申请日期

    private String ChannelOrderNo; //渠道订单号
    private String OrderNo; //订单编号

    private List<PtSpeedSegmentInfo> TicketPayoutDetailList; // 客票航段详情

}
