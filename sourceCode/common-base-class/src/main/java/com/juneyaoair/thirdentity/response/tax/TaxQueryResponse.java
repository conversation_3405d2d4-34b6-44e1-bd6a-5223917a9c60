package com.juneyaoair.thirdentity.response.tax;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-13.
 */
@XmlRootElement(name = "TaxQueryResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class TaxQueryResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息

    private String CurrencyCode;/// 币种
    private List<PassengerTaxInfo> PassengerTaxInfoList;// 乘客税费信息
    private String ROE;/// 1NUC与始发国货币汇率
    private String Rate;/// 1始发国货币与支付货币汇率

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        CurrencyCode = currencyCode;
    }

    public List<PassengerTaxInfo> getPassengerTaxInfoList() {
        return PassengerTaxInfoList;
    }

    public void setPassengerTaxInfoList(List<PassengerTaxInfo> passengerTaxInfoList) {
        PassengerTaxInfoList = passengerTaxInfoList;
    }

    public String getROE() {
        return ROE;
    }

    public void setROE(String ROE) {
        this.ROE = ROE;
    }

    public String getRate() {
        return Rate;
    }

    public void setRate(String rate) {
        Rate = rate;
    }
}
