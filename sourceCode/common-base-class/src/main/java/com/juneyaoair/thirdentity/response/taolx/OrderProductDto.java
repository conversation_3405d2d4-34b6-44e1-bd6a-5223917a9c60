package com.juneyaoair.thirdentity.response.taolx;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2017/7/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderProductDto {
    private Integer ID;//度假信息主键
    private Integer OrderID;//订单主键
    private String OrderNo;//订单号
    private String Name;//产品名字
    private String SubName;//产品副标题
    private String DepDate;//出发日期
    private String BakDate;//返回日期
    private Integer Days;//出行几天
    private Integer Nights;//出行几晚
    private String SkuName;//套餐名称
    private String SkuId;//套餐ID
    private String ReturnRule;//退改规则
    private String ProductId;//产品ID
    private String ProductType;//产品类型
    private String ProductImage;//图片封面图片
}
