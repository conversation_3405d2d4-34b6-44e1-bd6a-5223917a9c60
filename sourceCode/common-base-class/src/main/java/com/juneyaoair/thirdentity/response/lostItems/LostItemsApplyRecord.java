package com.juneyaoair.thirdentity.response.lostItems;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 遗失物品申请记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LostItemsApplyRecord {

    /**
     * 申请单ID
     */
    private String id;

    /**
     * 报失人姓名
     */
    private String reporterName;
    /**
     * 联系电话
     */
    private String reporterContact;
    /**
     * 报失日期
     */
    private String reporterDate;
    /**
     * 报失状态
     * 枚举
     * @see com.juneyaoair.appenum.lostitems.LostItemsApplyStatus
     */
    private String reporterStatus;
    /**
     * 报失编号
     */
    private String reportLossNo;
    /**
     * 航班日期
     * yyyy-MM-dd
     */
    private String flightDate;
    /**
     * 航班号
     */
    private String rlFlightNo;
    /**
     * 座位号
     */
    private String seatNo;
    /**
     * 会员id
     */
    private String memberId;
    /**
     * 出发站
     * 三字码
     */
    private String departureAirport;
    /**
     * 到达站
     * 三字码
     */
    private String arrivalAirport;
    /**
     * 物品描述
     */
    private String leaveBehindDesc;
    /**
     * 物品级别
     * 枚举
     * @see com.juneyaoair.appenum.lostitems.LostItemsType
     */
    private String leaveBehindLevel;
    /**
     * 备注
     */
    private String lbRemark;
    /**
     * 遗失地
     * 枚举
     * @see com.juneyaoair.appenum.lostitems.LostPlace
     */
    private String lossPlace;
    /**
     * 附件地址
     * 多个附件之间使用","分隔
     */
    private String filePath;
    //以下为自定义字段
    /**
     * 状态描述
     */
    private String reporterStatusDesc;
    /**
     * 遗失物品类型描述
     */
    private String leaveBehindLevelDesc;
    /**
     * 遗失地描述
     */
    private String lossPlaceDesc;
    /**
     * 出发城市
     */
    private String depCity;
    /**
     * 到达城市
     */
    private String arrCity;
    /**
     * 周几
     */
    private String week;

    /**
     * 报失标题
     */
    private String title;

    /**
     * 认领电话
     */
    private String toClaimThePhone;

}
