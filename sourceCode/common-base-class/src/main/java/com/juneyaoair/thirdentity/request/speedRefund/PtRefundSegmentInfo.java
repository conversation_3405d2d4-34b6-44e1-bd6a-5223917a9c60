package com.juneyaoair.thirdentity.request.speedRefund;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/1/16.
 */
@XmlRootElement(name = "RefundSegmentInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtRefundSegmentInfo {
    private Double RefundAmount;//实退金额
    private Double PriceAmount;//票价
    private Double RefundDeduction;//退票手续费
    private Double UpgradeFee;//升舱手续费
    private Double YQTax;//燃油费
    private Double CNTax;//建设税
    private int SegmentIndex;
    private String TicketNo;//客票号
    private String TicketStatus;//客票状态  可用，已退，换开
    private String SegmentStatus;//航段状态 正常，取消，延误
    private String FlightNo;//航班号
    private String DepDateTime;//航班起飞时间  yyyy-MM-dd HH:mm
    private String ArrDateTime;//航班到达时间  yyyy-MM-dd HH:mm
    private String DepCity;//起飞城市三字码
    private String ArrCity;//到达城市三字码
    private String DepAirport;//起飞机场三字码
    private String ArrAirport;//到达机场三字码
    private String Cabin;//舱位
    private String PlaneStyle;//机型

    public PtRefundSegmentInfo() {
    }

    public Double getRefundAmount() {
        return RefundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        RefundAmount = refundAmount;
    }

    public Double getPriceAmount() {
        return PriceAmount;
    }

    public void setPriceAmount(Double priceAmount) {
        PriceAmount = priceAmount;
    }

    public Double getRefundDeduction() {
        return RefundDeduction;
    }

    public void setRefundDeduction(Double refundDeduction) {
        RefundDeduction = refundDeduction;
    }

    public Double getUpgradeFee() {
        return UpgradeFee;
    }

    public void setUpgradeFee(Double upgradeFee) {
        UpgradeFee = upgradeFee;
    }

    public Double getYQTax() {
        return YQTax;
    }

    public void setYQTax(Double YQTax) {
        this.YQTax = YQTax;
    }

    public Double getCNTax() {
        return CNTax;
    }

    public void setCNTax(Double CNTax) {
        this.CNTax = CNTax;
    }

    public int getSegmentIndex() {
        return SegmentIndex;
    }

    public void setSegmentIndex(int segmentIndex) {
        SegmentIndex = segmentIndex;
    }

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getTicketStatus() {
        return TicketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        TicketStatus = ticketStatus;
    }

    public String getSegmentStatus() {
        return SegmentStatus;
    }

    public void setSegmentStatus(String segmentStatus) {
        SegmentStatus = segmentStatus;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getDepDateTime() {
        return DepDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        DepDateTime = depDateTime;
    }

    public String getArrDateTime() {
        return ArrDateTime;
    }

    public void setArrDateTime(String arrDateTime) {
        ArrDateTime = arrDateTime;
    }

    public String getDepCity() {
        return DepCity;
    }

    public void setDepCity(String depCity) {
        DepCity = depCity;
    }

    public String getArrCity() {
        return ArrCity;
    }

    public void setArrCity(String arrCity) {
        ArrCity = arrCity;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public String getPlaneStyle() {
        return PlaneStyle;
    }

    public void setPlaneStyle(String planeStyle) {
        PlaneStyle = planeStyle;
    }
}
