package com.juneyaoair.thirdentity.request.BaggageExcess;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 9:46 2018/1/7
 * @Modified by:
 */
@XmlRootElement(name = "BaggageExcess")
@XmlAccessorType(XmlAccessType.FIELD)
public class BaggageExcess {
    private String WeightOrderId;
    private String Title;
    private int Overweight;
    private String Unit;
    private Double ExcessAmount;
    private Double ExcessCNYAmount;
    private Double ExcessUSDAmount;
    private Double ExcessJPYAmount;
    private Double SingleCNYAmount;
    private Double SingleUSDAmount;
    private Double SingleJPYAmount;
    private String Is_Single;
    private String FlightStartDate;
    private String FlightEndDate;
    private String SuitChannel;
    private String Remark;
    private String FlightNo;

    private String ArrAirport;
    private String DepAirport;

    public BaggageExcess() {
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getWeightOrderId() {
        return WeightOrderId;
    }

    public void setWeightOrderId(String weightOrderId) {
        WeightOrderId = weightOrderId;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String title) {
        Title = title;
    }

    public int getOverweight() {
        return Overweight;
    }

    public void setOverweight(int overweight) {
        Overweight = overweight;
    }

    public String getUnit() {
        return Unit;
    }

    public void setUnit(String unit) {
        Unit = unit;
    }

    public Double getExcessAmount() {
        return ExcessAmount;
    }

    public void setExcessAmount(Double excessAmount) {
        ExcessAmount = excessAmount;
    }

    public Double getExcessCNYAmount() {
        return ExcessCNYAmount;
    }

    public void setExcessCNYAmount(Double excessCNYAmount) {
        ExcessCNYAmount = excessCNYAmount;
    }

    public Double getExcessUSDAmount() {
        return ExcessUSDAmount;
    }

    public void setExcessUSDAmount(Double excessUSDAmount) {
        ExcessUSDAmount = excessUSDAmount;
    }

    public Double getExcessJPYAmount() {
        return ExcessJPYAmount;
    }

    public void setExcessJPYAmount(Double excessJPYAmount) {
        ExcessJPYAmount = excessJPYAmount;
    }

    public Double getSingleCNYAmount() {
        return SingleCNYAmount;
    }

    public void setSingleCNYAmount(Double singleCNYAmount) {
        SingleCNYAmount = singleCNYAmount;
    }

    public Double getSingleUSDAmount() {
        return SingleUSDAmount;
    }

    public void setSingleUSDAmount(Double singleUSDAmount) {
        SingleUSDAmount = singleUSDAmount;
    }

    public Double getSingleJPYAmount() {
        return SingleJPYAmount;
    }

    public void setSingleJPYAmount(Double singleJPYAmount) {
        SingleJPYAmount = singleJPYAmount;
    }

    public String getIs_Single() {
        return Is_Single;
    }

    public void setIs_Single(String is_Single) {
        Is_Single = is_Single;
    }

    public String getFlightStartDate() {
        return FlightStartDate;
    }

    public void setFlightStartDate(String flightStartDate) {
        FlightStartDate = flightStartDate;
    }

    public String getFlightEndDate() {
        return FlightEndDate;
    }

    public void setFlightEndDate(String flightEndDate) {
        FlightEndDate = flightEndDate;
    }

    public String getSuitChannel() {
        return SuitChannel;
    }

    public void setSuitChannel(String suitChannel) {
        SuitChannel = suitChannel;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }
}
