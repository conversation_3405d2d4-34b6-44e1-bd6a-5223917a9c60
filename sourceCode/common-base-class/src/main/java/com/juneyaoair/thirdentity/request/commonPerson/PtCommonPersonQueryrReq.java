package com.juneyaoair.thirdentity.request.commonPerson;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PtCommonPersonQueryrReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtCommonPersonQueryrReq {
	
	private String Version; // 接口版本号10
	private String ChannelCode; // 渠道用户号B2C,CC等
	private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
	
	private String ChannelCustomerNo; // 渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID
	private String ChannelCustomerType; // 渠道客户类型,CRM - 常客,Other - 其它
	private String SortType; // 排序选项,LastBooking - 最近预订时间从近到远，BookingNum - 预订次数从大到小，Create － 创建时间从近到远  LastBookingDCreateA - 最近预定时间和创建时间
	private String PassengerName; // 乘客姓名
	private String CertNo; // 证件号
	private String InterFlag;
	private int PageNo; // 页码
	private int PageSize; // 每页大小5~50条之间
	
	
	public PtCommonPersonQueryrReq() {
		super();
	}
	
	public PtCommonPersonQueryrReq(String version,String userNo, String channelCode,
								   String channelCustomerType,String sortType,int pageNo,int pageSize) {
		super();
		Version = version;
		ChannelCode = channelCode;
		UserNo = userNo;
		ChannelCustomerType = channelCustomerType;
		SortType = sortType;
		PageNo=pageNo;
		PageSize=pageSize;
	}
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getChannelCustomerNo() {
		return ChannelCustomerNo;
	}
	public void setChannelCustomerNo(String channelCustomerNo) {
		ChannelCustomerNo = channelCustomerNo;
	}
	public String getChannelCustomerType() {
		return ChannelCustomerType;
	}
	public void setChannelCustomerType(String channelCustomerType) {
		ChannelCustomerType = channelCustomerType;
	}
	public String getSortType() {
		return SortType;
	}
	public void setSortType(String sortType) {
		SortType = sortType;
	}
	public String getPassengerName() {
		return PassengerName;
	}
	public void setPassengerName(String passengerName) {
		PassengerName = passengerName;
	}
	public String getCertNo() {
		return CertNo;
	}
	public void setCertNo(String certNo) {
		CertNo = certNo;
	}
	public int getPageNo() {
		return PageNo;
	}
	public void setPageNo(int pageNo) {
		PageNo = pageNo;
	}
	public int getPageSize() {
		return PageSize;
	}
	public void setPageSize(int pageSize) {
		PageSize = pageSize;
	}
	public String getInterFlag() {
		return InterFlag;
	}

	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}
}