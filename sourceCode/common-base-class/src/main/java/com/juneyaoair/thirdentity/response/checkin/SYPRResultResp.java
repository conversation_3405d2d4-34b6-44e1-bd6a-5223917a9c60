package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "SYPRResultResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class SYPRResultResp {
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息

	private List<SYPRSegInfo> segInfoList ;

	public String getResultCode() {
		return ResultCode;
	}

	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}

	public String getErrorInfo() {
		return ErrorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}

	public List<SYPRSegInfo> getSegInfoList() {
		return segInfoList;
	}

	public void setSegInfoList(List<SYPRSegInfo> segInfoList) {
		this.segInfoList = segInfoList;
	}
}
