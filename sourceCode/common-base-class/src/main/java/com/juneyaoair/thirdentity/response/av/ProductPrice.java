package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "ProductPrice")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"PriceProductType","PriceRouteType","PriceList"})
public class ProductPrice {
	
	private	String	PriceProductType	;	//	运价产品类型	1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程
	private	String	PriceRouteType	;	//	运价航程类型	OW - 单程,RT - 往返
	private	Price[]	PriceList	;	//	运价信息	OW+OW,OW+1/2RT
	
	public String getPriceProductType() {
		return PriceProductType;
	}
	public void setPriceProductType(String priceProductType) {
		PriceProductType = priceProductType;
	}
	public String getPriceRouteType() {
		return PriceRouteType;
	}
	public void setPriceRouteType(String priceRouteType) {
		PriceRouteType = priceRouteType;
	}
	public Price[] getPriceList() {
		return PriceList;
	}
	public void setPriceList(Price[] priceList) {
		PriceList = priceList;
	}
	
	@Override
	public String toString() {
		return "ProductPrice [PriceProductType=" + PriceProductType
				+ ", PriceRouteType=" + PriceRouteType + ", PriceList="
				+ Arrays.toString(PriceList) + "]";
	}
	
	

}
