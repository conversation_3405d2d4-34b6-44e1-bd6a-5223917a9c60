package com.juneyaoair.thirdentity.request.BaggageExcess;

import com.juneyaoair.baseclass.response.coupons.AvailCoupon;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 13:48 2018/1/8
 * @Modified by:
 */
@XmlRootElement(name = "BaggageExcessCouponInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class BaggageExcessCouponInfo extends AvailCoupon{
    //行李券中逾重行李产品信息
    private BaggageExcess baggageExcessInfo;
    private String depAirport;
    private String arrAirport;

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getArrAirport() {
        return arrAirport;
    }

    public void setArrAirport(String arrAirport) {
        this.arrAirport = arrAirport;
    }

    public BaggageExcessCouponInfo() {
    }

    public BaggageExcess getBaggageExcessInfo() {
        return baggageExcessInfo;
    }

    public void setBaggageExcessInfo(BaggageExcess baggageExcessInfo) {
        this.baggageExcessInfo = baggageExcessInfo;
    }
}
