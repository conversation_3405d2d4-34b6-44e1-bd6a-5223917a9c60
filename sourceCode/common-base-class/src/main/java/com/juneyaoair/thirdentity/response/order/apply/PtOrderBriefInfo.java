package com.juneyaoair.thirdentity.response.order.apply;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-6-2.
 */
@XmlRootElement(name = "OrderBriefInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtOrderBriefInfo {
    private String ChannelOrderNo;
    private String OrderNo;
    private String RouteType;
    private String FlightType;
    private String DepCityName;
    private String ArrCityName;
    private String OrderState;
    private String PayState;
    private String CurrencyCode;
    private Double Amount;
    private String CreateDatetime;

    public PtOrderBriefInfo() {
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getRouteType() {
        return RouteType;
    }

    public void setRouteType(String routeType) {
        RouteType = routeType;
    }

    public String getFlightType() {
        return FlightType;
    }

    public void setFlightType(String flightType) {
        FlightType = flightType;
    }

    public String getDepCityName() {
        return DepCityName;
    }

    public void setDepCityName(String depCityName) {
        DepCityName = depCityName;
    }

    public String getArrCityName() {
        return ArrCityName;
    }

    public void setArrCityName(String arrCityName) {
        ArrCityName = arrCityName;
    }

    public String getOrderState() {
        return OrderState;
    }

    public void setOrderState(String orderState) {
        OrderState = orderState;
    }

    public String getPayState() {
        return PayState;
    }

    public void setPayState(String payState) {
        PayState = payState;
    }

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        CurrencyCode = currencyCode;
    }

    public Double getAmount() {
        return Amount;
    }

    public void setAmount(Double amount) {
        Amount = amount;
    }

    public String getCreateDatetime() {
        return CreateDatetime;
    }

    public void setCreateDatetime(String createDatetime) {
        CreateDatetime = createDatetime;
    }
}
