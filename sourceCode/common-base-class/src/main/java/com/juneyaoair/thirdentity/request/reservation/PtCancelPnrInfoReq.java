package com.juneyaoair.thirdentity.request.reservation;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @ClassName PtCancelPnrInfoReq
 * <AUTHOR>
 * @Description
 * @Date 2021-08-04 9:19
 **/
@XmlRootElement(name = "PtCancelPnrInfoReq")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PtCancelPnrInfoReq {
    private String Version; // 接口版本号
    private String ChannelCode; // 渠道用户号
    private String UserNo; // 渠道工作人员号
    private String ChannelCustomerNo; //渠道客户编号 ffpId
    private String TicketNo; // 电子客票号
    private String FfpId; // 会员id
    private String FfCardNo; // 会员卡号
    private String FfpCardType; //常旅客类型
    private String PName; // 乘客姓名
    private String CardNo; // 证件号
    private String PType; // 乘客类型
    private String Pnr; // pnr编号
    private String Tel; // 手机号
    private String Remark;//取消订座详情信息

    public PtCancelPnrInfoReq(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }
}
