package com.juneyaoair.thirdentity.passengers.resp;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PeerPassenger {
    private String PassengerType;
    private String PassengerName;
    private String CertType;
    private String CertNo;
    private List<String> TicketNos;
    private String IsSelectFlag;
    private Date  Birthdate;
    private List<PeerCabinDtos> PeerCabinDtos;

}
