package com.juneyaoair.thirdentity.response.order.refund.calculate;

import com.juneyaoair.thirdentity.comm.RefundGroup;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "RefundResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class RefundResponse {
	private String Version; //接口版本号
	private String ChannelCode; //渠道用户号
	private String UserNo; //渠道工作人员号
	private String RouteType; //航程类型	单程：OW；往返：RT
	private String TicketingDate;
	private String InterFlag; //国际国内标识	D － 国内，I － 国际
	private List<RefundGroup> RefundGroupList;
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	/**
	 * true-自愿退票
	 * Y-非自愿 N-自愿
	 */
	private String Involuntary;

	public String getInvoluntary() {
		return Involuntary;
	}

	public void setInvoluntary(String involuntary) {
		Involuntary = involuntary;
	}

	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getRouteType() {
		return RouteType;
	}
	public void setRouteType(String routeType) {
		RouteType = routeType;
	}
	public String getTicketingDate() {
		return TicketingDate;
	}
	public void setTicketingDate(String ticketingDate) {
		TicketingDate = ticketingDate;
	}
	public String getInterFlag() {
		return InterFlag;
	}
	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}
	public List<RefundGroup> getRefundGroupList() {
		return RefundGroupList;
	}
	public void setRefundGroupList(List<RefundGroup> refundGroupList) {
		RefundGroupList = refundGroupList;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}
	

	
	
}
