package com.juneyaoair.thirdentity.response.speedRefund;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/25  15:48.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtTicketPayoutRetryResponse {
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String ChannelPayoutNo;  //渠道退款编号
    private String ChannelCustomerNo;  //渠道客户编号
    private String NewTicketPayoutNo;  //新客票退款编号
    private String ResultCode;  //结果代码
    private String ErrorInfo;   //错误信息
}
