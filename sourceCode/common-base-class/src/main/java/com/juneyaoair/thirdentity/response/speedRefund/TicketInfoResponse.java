package com.juneyaoair.thirdentity.response.speedRefund;

import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.TaxInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> by jiangmingming
 * @date 2019/1/10 15:21
 * 根据票号查询客票信息返回结果
 */
@Data
@NoArgsConstructor
public class TicketInfoResponse {
    private String ResultCode;//结果代码 1001 － 成功，其它失败
    private String ErrorInfo;//错误信息
    private String ChannelCode;//渠道用户号
    private String ChannelOrderNo;//渠道订单编号
    private String UserNo;//渠道工作人员号
    private boolean IsHasMoreTax; //是否有更多税  需要DETR:X提取的税项
    private boolean IsIT; //是否IT票
    private String EqviuCurrencyType;//等值支付货币
    private String EqviuFare;//等值支付金额
    private String ETicketType;//电子客票类型
    private String CurrencyType;//货币类型
    private String DstCity;//终点城市
    private String ExchangeInfo;//改签信息
    private Double Fare;//票价
    private String FareCompute;//票价计算信息
    private String FollowTicketNo;//后续票号
    private String ISI;//ISI信息
    private String IssueAirline;//出票航空公司
    private String OrgCity;//始发城市
    private String OriginalIssue;//OI信息
    private String PassengerName;//旅客姓名
    private String PayMethod;//支付方式
    private String SigningInfo;//签注信息
    private Double CN;//机场建设税
    private Double YQ;//燃油费
    private Double OB;//改期
    private Double Tax;//税款金额
    private String TicketNo;//票号
    private Double TotalAmount;//客票总金额
    private String TourCode;//旅游代码
    private String CurrencyTypeTotal;//票面总价的货币类型
    private String IsReceiptPrinted;//是否已打印T4（发票）联
    private String Remark;//原始主机返回信息
    private String InfantBirthday;//无人陪伴儿童年龄 yyyy-MM-dd
    private String PassengerType;//旅客类型
    private String UnaccompaniedChildAge;//无人陪伴儿童年龄
    private String IataNo;//出票的Iata号
    private String IssueDate;//旅游代码出票时间
    private String PassengerID;
    private List<PtSegmentInfo> SegmentInfoList;//航段信息列表
    private List<IdentityInfo> IdentityInfoList;//证件信息列表
    private List<TaxInfo> TaxInfoList;//税费信息列表
    private String InterFlag;//国际国内航班
    private String IsRefundValid;//是否可退票
    private String UnValidReson;//不可退原因
    private String IsFromDomestic;//是否国内出发
}
