package com.juneyaoair.thirdentity.response.tax;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-13.
 */

@XmlRootElement(name = "PassengerTaxInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PassengerTaxInfo {
    private	String	PassengerType	;	//	乘客类型
    private      List<InternatTaxInfo>	InternatTaxInfoList	;	//	税费信息列表
    private     List<QSegmentInfo> QSegmentInfoList	;	//	Q费信息列表
    private     Double ROE;/// 1NUC与始发国货币汇率
    private     Boolean IsSOTO;/// 是否SOTO
    private     String Rate;/// 1始发国货币与支付货币比值

    public PassengerTaxInfo() {
    }

    public String getPassengerType() {
        return PassengerType;
    }

    public void setPassengerType(String passengerType) {
        PassengerType = passengerType;
    }

    public List<InternatTaxInfo> getInternatTaxInfoList() {
        return InternatTaxInfoList;
    }

    public void setInternatTaxInfoList(List<InternatTaxInfo> internatTaxInfoList) {
        InternatTaxInfoList = internatTaxInfoList;
    }

    public List<QSegmentInfo> getQSegmentInfoList() {
        return QSegmentInfoList;
    }

    public void setQSegmentInfoList(List<QSegmentInfo> QSegmentInfoList) {
        this.QSegmentInfoList = QSegmentInfoList;
    }

    public Double getROE() {
        return ROE;
    }

    public void setROE(Double ROE) {
        this.ROE = ROE;
    }

    public Boolean getSOTO() {
        return IsSOTO;
    }

    public void setSOTO(Boolean SOTO) {
        IsSOTO = SOTO;
    }

    public String getRate() {
        return Rate;
    }

    public void setRate(String rate) {
        Rate = rate;
    }
}
