package com.juneyaoair.thirdentity.request.commonPerson;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

import com.juneyaoair.baseclass.response.commonPerson.CommonPersonInfo;


@XmlRootElement(name = "PtModifyCommonPersonReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtModifyCommonPersonReq {
	
	private String Version; // 接口版本号10
	private String ChannelCode; // 渠道用户号B2C,CC等
	private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
	
	private String ChannelCustomerNo; // 渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID
	private String ChannelCustomerType; // 渠道客户类型,CRM - 常客,Other - 其它
	private CommonPersonInfo PersonCommonInfo;//常用旅客联系信息
	private boolean IsRemove;//是否删除,进行物理删除该条记录
	
	
	public PtModifyCommonPersonReq() {
		super();
	}
	
	public PtModifyCommonPersonReq(String version,
			String userNo, String channelCode,String channelCustomerType) {
		super();
		Version = version;
		ChannelCode = channelCode;
		UserNo = userNo;
		ChannelCustomerType = channelCustomerType;
	}
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getChannelCustomerNo() {
		return ChannelCustomerNo;
	}
	public void setChannelCustomerNo(String channelCustomerNo) {
		ChannelCustomerNo = channelCustomerNo;
	}

	public String getChannelCustomerType() {
		return ChannelCustomerType;
	}

	public void setChannelCustomerType(String channelCustomerType) {
		ChannelCustomerType = channelCustomerType;
	}

	public CommonPersonInfo getPersonCommonInfo() {
		return PersonCommonInfo;
	}

	public void setPersonCommonInfo(CommonPersonInfo personCommonInfo) {
		PersonCommonInfo = personCommonInfo;
	}

	public boolean isIsRemove() {
		return IsRemove;
	}

	public void setIsRemove(boolean isRemove) {
		IsRemove = isRemove;
	}



	
}