package com.juneyaoair.thirdentity.response.av;

import com.juneyaoair.thirdentity.av.comm.PtChangeRuleInfo;
import com.juneyaoair.thirdentity.av.comm.PtRefundRuleInfo;

import java.util.Arrays;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "Price")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"PriceID","PassengerType","FareBasis","TourCode","Discount","EI","Comment","Baggage","ValidityPeriod",
		"MinStay","PriceValue","RSP","RefundedFlag","RefundedComment","ChangedComment","RescheduledFlag","FreeChangeTimes",
		"ChangeAirLineFlag","UpgradeFlag","YPrice","FareID","YQTax","CNTax","FareSign","DynamicCabin","DynamicFareID","CombineRuleInfo","ScoreUseInfoList","ScoreGiftInfo"})
public class Price {
	
	private	String	PriceID	;	//	运价ID	当次查询结果中唯一标识
	private	String	PassengerType	;	//	旅客类型	ADT-成人,CHD -儿童,INF - 婴儿
	private	String	FareBasis	;	//	运价基础	
	private	String	TourCode	;	//	旅行代码	
	private	String	Discount	;	//	折扣率	与Y公布运价的比率,如:值为10表示10%
	private	String	EI	;	//	限制条件	写入PNR的签注信息
	private	String	Comment	;	//	备注项	用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private	String	Baggage	;	//	行李重量	计重制:20KG 计件制:2PC
	private	String	ValidityPeriod	;	//	客票有效期(最长停留时间)	有效取值范围:1Y或1M或1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算	
	private	String	MinStay	;	//	最短停留时间	1Y1M1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
	private	Double	PriceValue	;	//	票价	票价金额为-1时是无效票价不能进行销售
	private	Double	RSP	;	//	销售参考价	用于前端宣传打折效果使用,当值为-1时表示找不到有效的销售参考价
	private	boolean 	RefundedFlag	;	//	是否可退票	
	private	String	RefundedComment	;	//	退票政策描述	说明退票费收取或计算方法
	private	String	ChangedComment	;	//	变更政策描述	说明改期、签转和升舱政策
	private	boolean 	RescheduledFlag	;	//	是否可免费改期	
	private	int	FreeChangeTimes	;	//	免费改期次数	免费改期不限次数时，目前运价系统会给出99次
	private	boolean	ChangeAirLineFlag	;	//	是否可改签	
	private	boolean	UpgradeFlag	;	//	是否可升舱	
	private	Double	YPrice	;	//	经济舱公布运价全价金额	
	private	String	FareID	;	//	运价编号	运价系统中该条运价的主键，计算退票费时有用
	private	Double	YQTax	;	//	燃油费	国内航程时该字段有效,国际时该字段无效值为-1
	private	Double	CNTax	;	//	建设税	国内航程时该字段有效,国际时该字段无效值为-1
	private	String	FareSign	;	//	运价验证串	Price中信息除本字段除外连接上运价系统的私钥做SHA1
	private	String	DynamicCabin;
	private	String	DynamicFareID;
	private	CombineRule	CombineRuleInfo	;	//	组合限制条件	
	private	ScoreUse[]	ScoreUseInfoList	;	//	积分使用信息	没有UseScore值相同的记录，当有UseScore值相同时只取ScoreUseRuleID大者
	private	ScoreGift	ScoreGiftInfo	;	//	积分赠送信息	有多个赠送规则符合时，只取ScoreGiftRuleID大者
	//2018-03-29 16:55:13新增
	private Double RefundFee;  		//起飞前两小时前退票费
	private Double RefundFeeAfter;	//起飞前两小时后退票费
	private Double ChangeFee;      	//起飞前两小时前变更费
	private Double ChangeFeeAfter; 	//起飞前两小时后变更费
	//退改规则列表
	private List<PtRefundRuleInfo> RefundedRules; //退票规则信息  RefundedFlag为false时该字段信息为空
	private List<PtChangeRuleInfo> ChangeRules ; //更改规则信息 UpgradeFlag为false并且RescheduledFlag为true时该字段信息为空

	public String getPriceID() {
		return PriceID;
	}
	public void setPriceID(String priceID) {
		PriceID = priceID;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public String getFareBasis() {
		return FareBasis;
	}
	public void setFareBasis(String fareBasis) {
		FareBasis = fareBasis;
	}
	public String getTourCode() {
		return TourCode;
	}
	public void setTourCode(String tourCode) {
		TourCode = tourCode;
	}
	public String getDiscount() {
		return Discount;
	}
	public void setDiscount(String discount) {
		Discount = discount;
	}
	public String getEI() {
		return EI;
	}
	public void setEI(String eI) {
		EI = eI;
	}
	public String getComment() {
		return Comment;
	}
	public void setComment(String comment) {
		Comment = comment;
	}
	public String getBaggage() {
		return Baggage;
	}
	public void setBaggage(String baggage) {
		Baggage = baggage;
	}
	public String getValidityPeriod() {
		return ValidityPeriod;
	}
	public void setValidityPeriod(String validityPeriod) {
		ValidityPeriod = validityPeriod;
	}
	public String getMinStay() {
		return MinStay;
	}
	public void setMinStay(String minStay) {
		MinStay = minStay;
	}
	public Double getPriceValue() {
		return PriceValue;
	}
	public void setPriceValue(Double priceValue) {
		PriceValue = priceValue;
	}
	public Double getRSP() {
		return RSP;
	}
	public void setRSP(Double rSP) {
		RSP = rSP;
	}
	public boolean getRefundedFlag() {
		return RefundedFlag;
	}
	public void setRefundedFlag(boolean refundedFlag) {
		RefundedFlag = refundedFlag;
	}
	public String getRefundedComment() {
		return RefundedComment;
	}
	public void setRefundedComment(String refundedComment) {
		RefundedComment = refundedComment;
	}
	public String getChangedComment() {
		return ChangedComment;
	}
	public void setChangedComment(String changedComment) {
		ChangedComment = changedComment;
	}
	public boolean getRescheduledFlag() {
		return RescheduledFlag;
	}
	public void setRescheduledFlag(boolean rescheduledFlag) {
		RescheduledFlag = rescheduledFlag;
	}
	public int getFreeChangeTimes() {
		return FreeChangeTimes;
	}
	public void setFreeChangeTimes(int freeChangeTimes) {
		FreeChangeTimes = freeChangeTimes;
	}
	public boolean getChangeAirLineFlag() {
		return ChangeAirLineFlag;
	}
	public void setChangeAirLineFlag(boolean changeAirLineFlag) {
		ChangeAirLineFlag = changeAirLineFlag;
	}
	public boolean getUpgradeFlag() {
		return UpgradeFlag;
	}
	public void setUpgradeFlag(boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}
	public Double getYPrice() {
		return YPrice;
	}
	public void setYPrice(Double yPrice) {
		YPrice = yPrice;
	}
	public String getFareID() {
		return FareID;
	}
	public void setFareID(String fareID) {
		FareID = fareID;
	}
	public Double getYQTax() {
		return YQTax;
	}
	public void setYQTax(Double yQTax) {
		YQTax = yQTax;
	}
	public Double getCNTax() {
		return CNTax;
	}
	public void setCNTax(Double cNTax) {
		CNTax = cNTax;
	}
	public String getFareSign() {
		return FareSign;
	}
	public void setFareSign(String fareSign) {
		FareSign = fareSign;
	}
	public CombineRule getCombineRuleInfo() {
		return CombineRuleInfo;
	}
	public void setCombineRuleInfo(CombineRule combineRuleInfo) {
		CombineRuleInfo = combineRuleInfo;
	}
	public ScoreUse[] getScoreUseInfoList() {
		return ScoreUseInfoList;
	}
	public void setScoreUseInfoList(ScoreUse[] scoreUseInfoList) {
		ScoreUseInfoList = scoreUseInfoList;
	}
	public ScoreGift getScoreGiftInfo() {
		return ScoreGiftInfo;
	}
	public void setScoreGiftInfo(ScoreGift scoreGiftInfo) {
		ScoreGiftInfo = scoreGiftInfo;
	}	
	public String getDynamicCabin() {
		return DynamicCabin;
	}
	public void setDynamicCabin(String dynamicCabin) {
		DynamicCabin = dynamicCabin;
	}
	public String getDynamicFareID() {
		return DynamicFareID;
	}
	public void setDynamicFareID(String dynamicFareID) {
		DynamicFareID = dynamicFareID;
	}

	public boolean isRefundedFlag() {
		return RefundedFlag;
	}

	public Double getRefundFee() {
		return RefundFee;
	}

	public void setRefundFee(Double refundFee) {
		RefundFee = refundFee;
	}

	public Double getRefundFeeAfter() {
		return RefundFeeAfter;
	}

	public void setRefundFeeAfter(Double refundFeeAfter) {
		RefundFeeAfter = refundFeeAfter;
	}

	public Double getChangeFee() {
		return ChangeFee;
	}

	public void setChangeFee(Double changeFee) {
		ChangeFee = changeFee;
	}

	public Double getChangeFeeAfter() {
		return ChangeFeeAfter;
	}

	public void setChangeFeeAfter(Double changeFeeAfter) {
		ChangeFeeAfter = changeFeeAfter;
	}

	public List<PtRefundRuleInfo> getRefundedRules() {
		return RefundedRules;
	}

	public void setRefundedRules(List<PtRefundRuleInfo> refundedRules) {
		RefundedRules = refundedRules;
	}

	public List<PtChangeRuleInfo> getChangeRules() {
		return ChangeRules;
	}

	public void setChangeRules(List<PtChangeRuleInfo> changeRules) {
		ChangeRules = changeRules;
	}

	@Override
	public String toString() {
		return "Price [PriceID=" + PriceID + ", PassengerType=" + PassengerType
				+ ", FareBasis=" + FareBasis + ", TourCode=" + TourCode
				+ ", Discount=" + Discount + ", EI=" + EI + ", Comment="
				+ Comment + ", Baggage=" + Baggage + ", ValidityPeriod="
				+ ValidityPeriod + ", MinStay=" + MinStay + ", PriceValue="
				+ PriceValue + ", RSP=" + RSP + ", RefundedFlag="
				+ RefundedFlag + ", RefundedComment=" + RefundedComment
				+ ", ChangedComment=" + ChangedComment + ", RescheduledFlag="
				+ RescheduledFlag + ", FreeChangeTimes=" + FreeChangeTimes
				+ ", ChangeAirLineFlag=" + ChangeAirLineFlag + ", UpgradeFlag="
				+ UpgradeFlag + ", YPrice=" + YPrice + ", FareID=" + FareID
				+ ", YQTax=" + YQTax + ", CNTax=" + CNTax + ", FareSign="
				+ FareSign + ", CombineRuleInfo=" + CombineRuleInfo
				+ ", ScoreUseInfoList=" + Arrays.toString(ScoreUseInfoList)
				+ ", ScoreGiftInfo=" + ScoreGiftInfo + "]";
	}
	
	

}
