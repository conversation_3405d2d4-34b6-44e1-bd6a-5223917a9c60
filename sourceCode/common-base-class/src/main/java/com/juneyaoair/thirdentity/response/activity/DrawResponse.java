package com.juneyaoair.thirdentity.response.activity;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "DrawResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class DrawResponse {
	private String Version;
	private String ChannelCode;
	private String ResultCode;
	private String ErrorInfo;
	private String PrizeId;
	private String PrizeName;
	private String WinId;
	private int drawIndex;
	private String CouponNo;
	private String CouponPwd;
	private String FfpId;
	private String FfpCardNo;
	private String LoginKeyInfo;

	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}
	public String getPrizeId() {
		return PrizeId;
	}
	public void setPrizeId(String prizeId) {
		PrizeId = prizeId;
	}
	public String getPrizeName() {
		return PrizeName;
	}
	public void setPrizeName(String prizeName) {
		PrizeName = prizeName;
	}
	public String getWinId() {
		return WinId;
	}
	public void setWinId(String winId) {
		WinId = winId;
	}
	public int getDrawIndex() {
		return drawIndex;
	}
	public void setDrawIndex(int drawIndex) {
		this.drawIndex = drawIndex;
	}
	public String getCouponNo() {
		return CouponNo;
	}
	public void setCouponNo(String couponNo) {
		CouponNo = couponNo;
	}
	public String getCouponPwd() {
		return CouponPwd;
	}
	public void setCouponPwd(String couponPwd) {
		CouponPwd = couponPwd;
	}

	public String getFfpId() {
		return FfpId;
	}

	public void setFfpId(String ffpId) {
		FfpId = ffpId;
	}

	public String getFfpCardNo() {
		return FfpCardNo;
	}

	public void setFfpCardNo(String ffpCardNo) {
		FfpCardNo = ffpCardNo;
	}

	public String getLoginKeyInfo() {
		return LoginKeyInfo;
	}

	public void setLoginKeyInfo(String loginKeyInfo) {
		LoginKeyInfo = loginKeyInfo;
	}
}
