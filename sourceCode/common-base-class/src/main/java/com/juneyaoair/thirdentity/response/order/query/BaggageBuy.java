package com.juneyaoair.thirdentity.response.order.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2018/1/8.
 * 购买的行李信息
 */
@XmlRootElement(name = "BaggageBuy")
@XmlAccessorType(XmlAccessType.FIELD)
public class BaggageBuy {
    private long WeightOrderId;
    private String Currency;
    private String FlightNo;
    private String FlightDate;
    private String IsSingleOrder;
    private String PassengerName;
    private String PayState;
    private String ScoreUseState;
    private Double WeightNum;
    private Double WeightOrderAmount;
    private String WeightOrderName;
    private String WeightUnite;
    private String WeightOrderRemark;
    private String DepCityName;
    private String ArrCityName;

    public BaggageBuy() {
    }

    public long getWeightOrderId() {
        return WeightOrderId;
    }

    public void setWeightOrderId(long weightOrderId) {
        WeightOrderId = weightOrderId;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getFlightDate() {
        return FlightDate;
    }

    public void setFlightDate(String flightDate) {
        FlightDate = flightDate;
    }

    public String getIsSingleOrder() {
        return IsSingleOrder;
    }

    public void setIsSingleOrder(String isSingleOrder) {
        IsSingleOrder = isSingleOrder;
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }

    public String getPayState() {
        return PayState;
    }

    public void setPayState(String payState) {
        PayState = payState;
    }

    public String getScoreUseState() {
        return ScoreUseState;
    }

    public void setScoreUseState(String scoreUseState) {
        ScoreUseState = scoreUseState;
    }

    public Double getWeightNum() {
        return WeightNum;
    }

    public void setWeightNum(Double weightNum) {
        WeightNum = weightNum;
    }

    public Double getWeightOrderAmount() {
        return WeightOrderAmount;
    }

    public void setWeightOrderAmount(Double weightOrderAmount) {
        WeightOrderAmount = weightOrderAmount;
    }

    public String getWeightOrderName() {
        return WeightOrderName;
    }

    public void setWeightOrderName(String weightOrderName) {
        WeightOrderName = weightOrderName;
    }

    public String getWeightUnite() {
        return WeightUnite;
    }

    public void setWeightUnite(String weightUnite) {
        WeightUnite = weightUnite;
    }

    public String getWeightOrderRemark() {
        return WeightOrderRemark;
    }

    public void setWeightOrderRemark(String weightOrderRemark) {
        WeightOrderRemark = weightOrderRemark;
    }

    public String getDepCityName() {
        return DepCityName;
    }

    public void setDepCityName(String depCityName) {
        DepCityName = depCityName;
    }

    public String getArrCityName() {
        return ArrCityName;
    }

    public void setArrCityName(String arrCityName) {
        ArrCityName = arrCityName;
    }
}
