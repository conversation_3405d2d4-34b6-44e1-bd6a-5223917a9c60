package com.juneyaoair.thirdentity.response.payment.hrBank;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2017/4/14.
 */
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
public class PtTranDetail {
    private String attach;
    private String timeValid;
    private String outTradeNo;
    private String backUrl;
    private String body;
    private String totalFee;
    private String detail;
    private String goodsTag;
    private String mchName;
    private String mchID;
    private String spbillCreateIp;
    private String feeType;
    private String limitPay;
    private String confirmOrder;
    private String unpaidAmount;
    private String paidAmount;

    public String getAttach() {
        return attach;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public String getBody() {
        return body;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public String getDetail() {
        return detail;
    }

    public String getGoodsTag() {
        return goodsTag;
    }

    public String getMchID() {
        return mchID;
    }

    public String getMchName() {
        return mchName;
    }

    public String getFeeType() {
        return feeType;
    }

    public String getSpbillCreateIp() {
        return spbillCreateIp;
    }

    public String getTimeValid() {
        return timeValid;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getConfirmOrder() {
        return confirmOrder;
    }

    public String getLimitPay() {
        return limitPay;
    }

    public void setGoodsTag(String goodsTag) {
        this.goodsTag = goodsTag;
    }

    public void setConfirmOrder(String confirmOrder) {
        this.confirmOrder = confirmOrder;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public void setMchID(String mchID) {
        this.mchID = mchID;
    }

    public void setMchName(String mchName) {
        this.mchName = mchName;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public void setLimitPay(String limitPay) {
        this.limitPay = limitPay;
    }

    public void setTimeValid(String timeValid) {
        this.timeValid = timeValid;
    }

    public void setSpbillCreateIp(String spbillCreateIp) {
        this.spbillCreateIp = spbillCreateIp;
    }

    public String getUnpaidAmount() {
        return unpaidAmount;
    }

    public String getPaidAmount() {
        return paidAmount;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public void setPaidAmount(String paidAmount) {
        this.paidAmount = paidAmount;
    }

    public void setUnpaidAmount(String unpaidAmount) {
        this.unpaidAmount = unpaidAmount;
    }
}
