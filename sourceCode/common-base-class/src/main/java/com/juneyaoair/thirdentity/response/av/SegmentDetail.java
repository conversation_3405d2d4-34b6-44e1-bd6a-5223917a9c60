package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlRootElement(name = "SegmentDetail")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"SegDetailNO","DepAirport","ArrAirport","FlightInfoList"})
public class SegmentDetail {
	
	private	String	SegDetailNO	;	//	唯一标识航段机场序号	由旅行顺序+航段机场序号 第一段为从0_0开始，第二段为0_1，依次增加
	private	String	DepAirport	;	//	起始地机场三字码	此处的机场必须是属于Segment.DepCity
	private	String	ArrAirport	;	//	目的地机场三字码	此处的机场必须是属于Segment.ArrCity
	private	FlightInfo[]	FlightInfoList	;	//	航班条件明细	
	
	
	public String getSegDetailNO() {
		return SegDetailNO;
	}
	public void setSegDetailNO(String segDetailNO) {
		SegDetailNO = segDetailNO;
	}
	public String getDepAirport() {
		return DepAirport;
	}
	public void setDepAirport(String depAirport) {
		DepAirport = depAirport;
	}
	public String getArrAirport() {
		return ArrAirport;
	}
	public void setArrAirport(String arrAirport) {
		ArrAirport = arrAirport;
	}
	public FlightInfo[] getFlightInfoList() {
		return FlightInfoList;
	}
	public void setFlightInfoList(FlightInfo[] flightInfoList) {
		FlightInfoList = flightInfoList;
	}
	
	@Override
	public String toString() {
		return "SegmentDetail [SegDetailNO=" + SegDetailNO + ", DepAirport="
				+ DepAirport + ", ArrAirport=" + ArrAirport
				+ ", FlightInfoList=" + Arrays.toString(FlightInfoList) + "]";
	}
	
	
	


}
