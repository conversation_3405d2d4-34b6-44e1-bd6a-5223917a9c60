package com.juneyaoair.thirdentity.response.flightservice;

import java.util.List;

/**
 * Created by qinxiaoming on 2016-4-22.
 */
public class SegmentInfo {
    /// <summary>
    /// 旅行顺序	第一段为从0开始，第二段为1，依次增加
    /// </summary>
    private int SegNO;
    /// <summary>
    /// 航班日期 yyyy-mm-dd
    /// </summary>
    private String FlightDate;
    /// <summary>
    /// 起飞城市
    /// </summary>
    private String DepCity;
    /// <summary>
    /// 到达城市
    /// </summary>
    private String ArrCity;
    /// <summary>
    /// 可用积分舱位列表
    /// </summary>
    private List<String> ScoreCabinList;
    /// <summary>
    /// 可用优惠券舱位列表
    /// </summary>
    private List<String> CouponCabinList;
    /// <summary>
    /// 保险绑定舱位列表
    /// </summary>
    private List<String> InsuranceCabinList;

    public int getSegNO() {
        return SegNO;
    }

    public void setSegNO(int segNO) {
        SegNO = segNO;
    }

    public String getFlightDate() {
        return FlightDate;
    }

    public void setFlightDate(String flightDate) {
        FlightDate = flightDate;
    }

    public String getDepCity() {
        return DepCity;
    }

    public void setDepCity(String depCity) {
        DepCity = depCity;
    }

    public String getArrCity() {
        return ArrCity;
    }

    public void setArrCity(String arrCity) {
        ArrCity = arrCity;
    }

    public List<String> getScoreCabinList() {
        return ScoreCabinList;
    }

    public void setScoreCabinList(List<String> scoreCabinList) {
        ScoreCabinList = scoreCabinList;
    }

    public List<String> getCouponCabinList() {
        return CouponCabinList;
    }

    public void setCouponCabinList(List<String> couponCabinList) {
        CouponCabinList = couponCabinList;
    }

    public List<String> getInsuranceCabinList() {
        return InsuranceCabinList;
    }

    public void setInsuranceCabinList(List<String> insuranceCabinList) {
        InsuranceCabinList = insuranceCabinList;
    }
}
