package com.juneyaoair.thirdentity.request.speedRefund;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/1/16.
 */
@XmlRootElement(name = "PassengerInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtPassengerInfo {
    private String PassengerName;//乘客姓名
    private String PassengerType;//乘客类型 ADT － 成人，CHD － 儿童，INF － 婴儿
    private String PassengerIdentity;//乘客身份 VIP，教师，警殘，革伤,无成人陪伴儿童
    private String CertType;//证件类型 身份证:NI,护照:PP,其它证件:CC
    private String CertNo; //证件号码

    public PtPassengerInfo() {
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }

    public String getPassengerType() {
        return PassengerType;
    }

    public void setPassengerType(String passengerType) {
        PassengerType = passengerType;
    }

    public String getPassengerIdentity() {
        return PassengerIdentity;
    }

    public void setPassengerIdentity(String passengerIdentity) {
        PassengerIdentity = passengerIdentity;
    }

    public String getCertType() {
        return CertType;
    }

    public void setCertType(String certType) {
        CertType = certType;
    }

    public String getCertNo() {
        return CertNo;
    }

    public void setCertNo(String certNo) {
        CertNo = certNo;
    }
}
