package com.juneyaoair.thirdentity.tongdun;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/1/16 15:51
 */
public enum  LoginTypeEnum {
    MESSAGE("MESSAGE","1"),
    PASSWORD("PASSWORD","2"),
    ZHIFUBAO("ZHIFUBAO","3"),
    WEIXIN("WEIXIN","4"),
    QQ("QQ","5"),
    APPLE("APPLE", "6");
    private  String code;
    private  String name;
    LoginTypeEnum(String code,String name){
        this.code = code;
        this.name = name;
    }
    public static LoginTypeEnum checkEnum(String v){
        for (LoginTypeEnum c: LoginTypeEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
