package com.juneyaoair.thirdentity.brandright.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName BrandRightQueryRequest
 * <AUTHOR>
 * @Description
 * @Date 2021-05-11 16:52
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BrandRightQueryRequest {
    /**
     * 品牌权益 branCode+cabinClass+_tourCode
     */
    private String BrandRightCode; //品牌权益码
    private String LangCode; //语言
    private String Cabin; //仓位
}
