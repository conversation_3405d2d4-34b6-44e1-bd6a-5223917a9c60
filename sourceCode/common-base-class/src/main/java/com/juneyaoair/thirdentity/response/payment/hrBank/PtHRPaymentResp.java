package com.juneyaoair.thirdentity.response.payment.hrBank;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/4/14.
 */
@XmlRootElement(name = "PtHRPaymentResp")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtHRPaymentResp {
    private PtTranDetail tranDetail;
    private String random;
    private String sign;
    private PtAutoFillData autoFillData;
    private String RespCode;
}
