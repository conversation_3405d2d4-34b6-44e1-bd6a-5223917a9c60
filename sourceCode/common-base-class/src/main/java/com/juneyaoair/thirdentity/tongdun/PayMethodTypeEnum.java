package com.juneyaoair.thirdentity.tongdun;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/1/18 13:44
 */
public enum PayMethodTypeEnum {
    YUNSHANFU("YUNSHANFU","1"),
    ZHIFUBAO("ZHIF<PERSON>BAO","2"),
    WEIXIN("WEINXIN","3"),
    YINLIANKA("YINLIANKA","4"),
    XINYONGFEI("XINYONGFEI","5"),
    HUARUIYINHANG("HUARUIYINHANG","6"),
    YIWANGTONG("YIWANGTONG","7"),
    YIBAO("YIBAO","8"),
    YINHANGKA("YINHANGKA","9"),
    QITA("QITA","10");
    private  String code;
    private  String name;
    PayMethodTypeEnum(String code,String name){
        this.code = code;
        this.name = name;
    }
    public static PayMethodTypeEnum checkEnum(String v){
        for (PayMethodTypeEnum c: PayMethodTypeEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
