package com.juneyaoair.thirdentity.response.payment.hrBank;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2017/4/14.
 */
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
public class PtAutoFillData {
    private String realName;
    private String cardNo;
    private String identity;
    private String mobile;
    private String revmobile;

    public String getCardNo() {
        return cardNo;
    }

    public String getIdentity() {
        return identity;
    }

    public String getMobile() {
        return mobile;
    }

    public String getRealName() {
        return realName;
    }

    public String getRevmobile() {
        return revmobile;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public void setRevmobile(String revmobile) {
        this.revmobile = revmobile;
    }
}
