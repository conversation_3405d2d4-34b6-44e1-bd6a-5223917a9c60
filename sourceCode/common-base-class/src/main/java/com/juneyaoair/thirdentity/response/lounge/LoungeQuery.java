package com.juneyaoair.thirdentity.response.lounge;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
public class LoungeQuery {
    private String LoungeNo;/// 休息室编号
    private String Name;/// 名称
    private String Address;/// 地址
    private String AirportCode;/// 机场三字码
    private String Cabin;/// 舱位
    private String LoungeTitle;/// 休息室概要
    private String LoungeContent;/// 休息室介绍
    private String LoungePicUrl;/// 图片url集合
    private Double LoungeOrgAmount;/// 原金额
    private Double LoungeAmount;/// 销售金额
    private Double MaxUseScore;/// 可使用积分数
    private int SingleValidityDay;//单独购买有效期天数
    private int ValidityDay;//非单独购买有效期天数

    private int LoungeCount;/// 购买休息室份数
    private Double OrderAmount;/// 购买金额

    public LoungeQuery() {
        super();
    }

    public String getLoungeNo() {
        return LoungeNo;
    }

    public void setLoungeNo(String loungeNo) {
        LoungeNo = loungeNo;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public String getAirportCode() {
        return AirportCode;
    }

    public void setAirportCode(String airportCode) {
        AirportCode = airportCode;
    }

    public String getLoungeTitle() {
        return LoungeTitle;
    }

    public void setLoungeTitle(String loungeTitle) {
        LoungeTitle = loungeTitle;
    }

    public String getLoungeContent() {
        return LoungeContent;
    }

    public void setLoungeContent(String loungeContent) {
        LoungeContent = loungeContent;
    }

    public String getLoungePicUrl() {
        return LoungePicUrl;
    }

    public void setLoungePicUrl(String loungePicUrl) {
        LoungePicUrl = loungePicUrl;
    }

    public Double getLoungeOrgAmount() {
        return LoungeOrgAmount;
    }

    public void setLoungeOrgAmount(Double loungeOrgAmount) {
        LoungeOrgAmount = loungeOrgAmount;
    }

    public Double getLoungeAmount() {
        return LoungeAmount;
    }

    public void setLoungeAmount(Double loungeAmount) {
        LoungeAmount = loungeAmount;
    }

    public Double getMaxUseScore() {
        return MaxUseScore;
    }

    public void setMaxUseScore(Double maxUseScore) {
        MaxUseScore = maxUseScore;
    }

    public int getLoungeCount() {
        return LoungeCount;
    }

    public void setLoungeCount(int loungeCount) {
        LoungeCount = loungeCount;
    }

    public Double getOrderAmount() {
        return OrderAmount;
    }

    public void setOrderAmount(Double orderAmount) {
        OrderAmount = orderAmount;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public int getSingleValidityDay() {
        return SingleValidityDay;
    }

    public void setSingleValidityDay(int singleValidityDay) {
        SingleValidityDay = singleValidityDay;
    }

    public int getValidityDay() {
        return ValidityDay;
    }

    public void setValidityDay(int validityDay) {
        ValidityDay = validityDay;
    }
}
