package com.juneyaoair.thirdentity.response.speedRefund;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 快速退票航段信息原生（调用第三方系统，比如订单系统时）返回体
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSpeedSegmentInfo {
    private Integer TicketPayoutDetailId; //客票退款明细ID
    private Integer TicketPayoutId; // 客票退款ID
    private String ArrAirport; //到达机场三字码
    private String Cabin; // 舱位
    private String ChannelNo; //所属渠道用户号 例如“OMBILE”
    private Double TicketPayoutAmount; // 退款总金额
    private Double TichetAmount; // 票面金额
    private Double CnTax; // 机建税
    private Double YqTax; // 燃油费
    private Double OtherTax; // 其他税费
    private Double QFee; // Q费
    private Double RefundDeduction; // 退票手续费现金
    private Double UpgradeFee; // 升舱手续费
    private String DepAirport; // 起飞机场
    private String FlightDate; // 航班日期
    private String FlightNo; // 航班号
    private String SegmentIndex; // 票面航段序号
    private String SegmentStatus; // 航段状态
    private String TicketNo; // 票号
    private String TicketStatus; // 客票状态
}
