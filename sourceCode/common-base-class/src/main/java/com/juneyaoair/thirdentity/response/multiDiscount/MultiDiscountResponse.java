package com.juneyaoair.thirdentity.response.multiDiscount;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:17 2017/10/18
 * @Modified by:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiDiscountResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private int DiscountAmount;//优惠金额
    private String ResultCode;//1001 － 成功，其它失败
    private String ErrorInfo;
}
