package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "CombineRule")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"CombineSegList","Cabins","CombineId"})
public class CombineRule {
	
	private	String[]	CombineSegList	;	//	为空没有限制。有值时表示可以与该条运价组合的后续起飞到达机场对列表。数组中每一行一条起飞到达机场对	PVGPEK
	private	String[]	Cabins	;	//	为空没有限制。有值时表示可以和该条运价组合的舱位代码（CabinFare.CabinCode）。数组中每一行一个舱位代码（多程惠达会限制组合舱位，如：头等与头等，经济与经济）	
	private	String	CombineId	;	//	组合Id,为空没有限制,有值时相同值的CombineId的运价才能组合
	
	
	public String[] getCombineSegList() {
		return CombineSegList;
	}
	public void setCombineSegList(String[] combineSegList) {
		CombineSegList = combineSegList;
	}
	public String[] getCabins() {
		return Cabins;
	}
	public void setCabins(String[] cabins) {
		Cabins = cabins;
	}
	public String getCombineId() {
		return CombineId;
	}
	public void setCombineId(String combineId) {
		CombineId = combineId;
	}
	
	
	@Override
	public String toString() {
		return "CombineRule [CombineSegList=" + Arrays.toString(CombineSegList)
				+ ", Cabins=" + Arrays.toString(Cabins) + ", CombineId="
				+ CombineId + "]";
	}
	

}
