package com.juneyaoair.thirdentity.passengers.resp;

import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/9  13:15.
 */
@Data
public class PtV2GeneralContactResponse extends PtResponse {
    private List<GeneralContactInfo> GeneralContactList;
}
