package com.juneyaoair.thirdentity.response.speedRefund;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/7/2  16:29
 *@description: 订单系统原生退单详情
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class QueryRefundDetailResponse {
    private String ResultCode; // 状态码
    private String ErrorInfo; // 描述信息
    private String Version; // 版本号
    private String ChannelCode; // 渠道号 例如“WEIXIN”,“MOBILE”
    private String UserNo; // 渠道工作人员编号
    private TicketPayoutDetailInfo TicketPayoutDetailInfo; // 退票详情

}
