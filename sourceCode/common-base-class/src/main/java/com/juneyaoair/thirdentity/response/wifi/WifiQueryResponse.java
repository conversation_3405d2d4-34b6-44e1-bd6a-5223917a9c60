package com.juneyaoair.thirdentity.response.wifi;

import com.juneyaoair.baseclass.response.wifi.WifiQuery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
@XmlRootElement(name = "WifiQueryResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class WifiQueryResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息
    private String FfpId;
    private String FfpCardNo;
    private List<WifiQuery> WifiQueryList;

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public List<WifiQuery> getWifiQueryList() {
        return WifiQueryList;
    }

    public void setWifiQueryList(List<WifiQuery> wifiQueryList) {
        WifiQueryList = wifiQueryList;
    }
}
