package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlRootElement(name = "Segment")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"SegNO","FlightDirection","DepCity","ArrCity","FlightDate","SegmentDetailList"})
public class Segment {
	
	private	int	SegNO;	//	旅行顺序	第一段为从0开始，第二段为1，依次增加
	private	String	FlightDirection;	//	飞行方向	去程为G,回程为B
	private	String	DepCity;	//	起始地城市三字码	
	private	String	ArrCity;	//	目的地城市三字码	
	private	String	FlightDate;	//	航班日期	yyyy-MM-dd
	private	SegmentDetail[]	SegmentDetailList;	//	航程明细	
	
	
	public int getSegNO() {
		return SegNO;
	}
	public void setSegNO(int segNO) {
		SegNO = segNO;
	}
	public String getFlightDirection() {
		return FlightDirection;
	}
	public void setFlightDirection(String flightDirection) {
		FlightDirection = flightDirection;
	}
	public String getDepCity() {
		return DepCity;
	}
	public void setDepCity(String depCity) {
		DepCity = depCity;
	}
	public String getArrCity() {
		return ArrCity;
	}
	public void setArrCity(String arrCity) {
		ArrCity = arrCity;
	}
	public String getFlightDate() {
		return FlightDate;
	}
	public void setFlightDate(String flightDate) {
		FlightDate = flightDate;
	}
	public SegmentDetail[] getSegmentDetailList() {
		return SegmentDetailList;
	}
	public void setSegmentDetailList(SegmentDetail[] segmentDetailList) {
		SegmentDetailList = segmentDetailList;
	}
	
	
	@Override
	public String toString() {
		return "Segment [SegNO=" + SegNO + ", FlightDirection="
				+ FlightDirection + ", DepCity=" + DepCity + ", ArrCity="
				+ ArrCity + ", FlightDate=" + FlightDate
				+ ", SegmentDetailList=" + Arrays.toString(SegmentDetailList)
				+ "]";
	}
	
	

}
