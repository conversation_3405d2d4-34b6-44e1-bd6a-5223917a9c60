package com.juneyaoair.thirdentity.response.detr;

public class SegmentInfo {
	private String ArrAirportTerminal; //到达航站楼
	private String DepAirportTerminal; //起飞航站楼
	private String ArrAirportCode; //到达机场代码
	private String ArrAirportName;
	private String ArrCityName;
	private String ArrCityCode;
	private String BaggageWeight; //允许携带的行李重量
	private String BaggagePiece; //允许携带的行李件数
	private String Cabin; //预订舱位
	private String DepAirportCode; //起飞机场代码
	private String DepAirportName;
	private String DepCityName;
	private String DepCityCode;
	private String StartValidityDate; //有效期起始时间(null表示无起始日期或情况不明)yyyy-MM-dd HH:mi
	private String EndValidityDate; //有效期终止时间(null表示无终止日期或情况不明)yyyy-MM-dd HH:mi
	private String DepTime; //起飞时间yyyy-MM-dd HH:mi
	private String FlightNo; //航班编号
	private String PnrNo; //PNR编号(ICS编号)
	private String CrsPnrNo; //代理人系统PNR编号
	private String CrsType; //代理人系统代码
	private String Rate; //适用的运价类型（如YB为Y舱B类运价）FireBase
	private String TicketStatus; //客票状态
	private String Airline; //
	private String BoardingNo; //旅客在已飞行航段中的登机牌号
	private String SegmentStatus; //航段状态
	private String StopType; //停留原因停留原因 O 正常 X 中转联程
	private String Type; //航段类型
	private boolean IsFPC; //
	private String OperationAirline; //承运方航空公司代码
	private String MarketingAirline; //市场方航空公司代码
	private String ArrTime; //到达时间yyyy-MM-dd HH:mi
	private int SegmentIndex; //电子票票面航段序号
	private String McoNumber; //MCO单号
	private String BaggageWeightUnit; //行李重量单位
	private String SeatStatus; //座位状态 2021-09-14

	public String getSeatStatus() {
		return SeatStatus;
	}

	public void setSeatStatus(String seatStatus) {
		SeatStatus = seatStatus;
	}

	public String getArrAirportTerminal(){
		return ArrAirportTerminal;
	}
	public void setArrAirportTerminal(String ArrAirportTerminal){
		this.ArrAirportTerminal=ArrAirportTerminal;
	}
	public String getDepAirportTerminal(){
		return DepAirportTerminal;
	}
	public void setDepAirportTerminal(String DepAirportTerminal){
		this.DepAirportTerminal=DepAirportTerminal;
	}
	public String getArrAirportCode(){
		return ArrAirportCode;
	}
	public void setArrAirportCode(String ArrAirportCode){
		this.ArrAirportCode=ArrAirportCode;
	}
	public String getBaggageWeight(){
		return BaggageWeight;
	}
	public void setBaggageWeight(String BaggageWeight){
		this.BaggageWeight=BaggageWeight;
	}
	public String getBaggagePiece(){
		return BaggagePiece;
	}
	public void setBaggagePiece(String BaggagePiece){
		this.BaggagePiece=BaggagePiece;
	}
	public String getCabin(){
		return Cabin;
	}
	public void setCabin(String Cabin){
		this.Cabin=Cabin;
	}
	public String getDepAirportCode(){
		return DepAirportCode;
	}
	public void setDepAirportCode(String DepAirportCode){
		this.DepAirportCode=DepAirportCode;
	}
	public String getStartValidityDate(){
		return StartValidityDate;
	}
	public void setStartValidityDate(String StartValidityDate){
		this.StartValidityDate=StartValidityDate;
	}
	public String getEndValidityDate(){
		return EndValidityDate;
	}
	public void setEndValidityDate(String EndValidityDate){
		this.EndValidityDate=EndValidityDate;
	}
	public String getDepTime(){
		return DepTime;
	}
	public void setDepTime(String DepTime){
		this.DepTime=DepTime;
	}
	public String getFlightNo(){
		return FlightNo;
	}
	public void setFlightNo(String FlightNo){
		this.FlightNo=FlightNo;
	}
	public String getPnrNo(){
		return PnrNo;
	}
	public void setPnrNo(String PnrNo){
		this.PnrNo=PnrNo;
	}
	public String getCrsPnrNo(){
		return CrsPnrNo;
	}
	public void setCrsPnrNo(String CrsPnrNo){
		this.CrsPnrNo=CrsPnrNo;
	}
	public String getCrsType(){
		return CrsType;
	}
	public void setCrsType(String CrsType){
		this.CrsType=CrsType;
	}
	public String getRate(){
		return Rate;
	}
	public void setRate(String Rate){
		this.Rate=Rate;
	}
	public String getTicketStatus(){
		return TicketStatus;
	}
	public void setTicketStatus(String TicketStatus){
		this.TicketStatus=TicketStatus;
	}
	public String getAirline(){
		return Airline;
	}
	public void setAirline(String Airline){
		this.Airline=Airline;
	}
	public String getBoardingNo(){
		return BoardingNo;
	}
	public void setBoardingNo(String BoardingNo){
		this.BoardingNo=BoardingNo;
	}
	public String getSegmentStatus(){
		return SegmentStatus;
	}
	public void setSegmentStatus(String SegmentStatus){
		this.SegmentStatus=SegmentStatus;
	}
	public String getStopType(){
		return StopType;
	}
	public void setStopType(String StopType){
		this.StopType=StopType;
	}
	public String getType(){
		return Type;
	}
	public void setType(String Type){
		this.Type=Type;
	}
	public boolean getIsFPC(){
		return IsFPC;
	}
	public void setIsFPC(boolean IsFPC){
		this.IsFPC=IsFPC;
	}
	public String getOperationAirline(){
		return OperationAirline;
	}
	public void setOperationAirline(String OperationAirline){
		this.OperationAirline=OperationAirline;
	}
	public String getMarketingAirline(){
		return MarketingAirline;
	}
	public void setMarketingAirline(String MarketingAirline){
		this.MarketingAirline=MarketingAirline;
	}
	public String getArrTime(){
		return ArrTime;
	}
	public void setArrTime(String ArrTime){
		this.ArrTime=ArrTime;
	}
	public int getSegmentIndex(){
		return SegmentIndex;
	}
	public void setSegmentIndex(int SegmentIndex){
		this.SegmentIndex=SegmentIndex;
	}
	public String getMcoNumber(){
		return McoNumber;
	}
	public void setMcoNumber(String McoNumber){
		this.McoNumber=McoNumber;
	}
	public String getBaggageWeightUnit(){
		return BaggageWeightUnit;
	}
	public void setBaggageWeightUnit(String BaggageWeightUnit){
		this.BaggageWeightUnit=BaggageWeightUnit;
	}
	public String getArrAirportName() {
		return ArrAirportName;
	}
	public void setArrAirportName(String arrAirportName) {
		ArrAirportName = arrAirportName;
	}
	public String getDepAirportName() {
		return DepAirportName;
	}
	public void setDepAirportName(String depAirportName) {
		DepAirportName = depAirportName;
	}
	public String getArrCityName() {
		return ArrCityName;
	}
	public void setArrCityName(String arrCityName) {
		ArrCityName = arrCityName;
	}
	public String getDepCityName() {
		return DepCityName;
	}
	public void setDepCityName(String depCityName) {
		DepCityName = depCityName;
	}
	public String getArrCityCode() {
		return ArrCityCode;
	}

	public void setArrCityCode(String arrCityCode) {
		ArrCityCode = arrCityCode;
	}

	public String getDepCityCode() {
		return DepCityCode;
	}

	public void setDepCityCode(String depCityCode) {
		DepCityCode = depCityCode;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("SegmentInfo ["); 
		sb.append("ArrAirportTerminal="+ArrAirportTerminal+",");
		sb.append("DepAirportTerminal="+DepAirportTerminal+",");
		sb.append("ArrAirportCode="+ArrAirportCode+",");
		sb.append("BaggageWeight="+BaggageWeight+",");
		sb.append("BaggagePiece="+BaggagePiece+",");
		sb.append("Cabin="+Cabin+",");
		sb.append("DepCityName="+DepCityName+",");
		sb.append("ArrCityName="+ArrCityName+",");
		sb.append("DepAirportCode="+DepAirportCode+",");
		sb.append("StartValidityDate="+StartValidityDate+",");
		sb.append("EndValidityDate="+EndValidityDate+",");
		sb.append("DepTime="+DepTime+",");
		sb.append("FlightNo="+FlightNo+",");
		sb.append("PnrNo="+PnrNo+",");
		sb.append("CrsPnrNo="+CrsPnrNo+",");
		sb.append("CrsType="+CrsType+",");
		sb.append("Rate="+Rate+",");
		sb.append("TicketStatus="+TicketStatus+",");
		sb.append("Airline="+Airline+",");
		sb.append("BoardingNo="+BoardingNo+",");
		sb.append("SegmentStatus="+SegmentStatus+",");
		sb.append("StopType="+StopType+",");
		sb.append("Type="+Type+",");
		sb.append("IsFPC="+IsFPC+",");
		sb.append("OperationAirline="+OperationAirline+",");
		sb.append("MarketingAirline="+MarketingAirline+",");
		sb.append("ArrTime="+ArrTime+",");
		sb.append("SegmentIndex="+SegmentIndex+",");
		sb.append("McoNumber="+McoNumber+",");
		sb.append("BaggageWeightUnit="+BaggageWeightUnit);
		sb.append("]");
		return sb.toString();
	}
}