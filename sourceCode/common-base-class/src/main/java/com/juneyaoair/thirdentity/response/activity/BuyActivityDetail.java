package com.juneyaoair.thirdentity.response.activity;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.xml.datatype.XMLGregorianCalendar;

/** 我的众筹明细
 * Created by Administrator on 2017/10/30.
 */
@AllArgsConstructor
@NoArgsConstructor
public class BuyActivityDetail {
    private String UserId;
    private String MemberId;
    private String CertificateNo;
    private String Phone;
    private String ActivityCode;
    private String ActivityNo;
    private String ActivityName;
    private String Price;
    private String OrderNo;
    private String ChannelOrderNo;
    private String OrderStatus;
    private XMLGregorianCalendar PayTime;
    private int RecordNo;
    private String RecordStatus;
    private String Remark;
    private String payTimeStr;

    public String getUserId() {
        return UserId;
    }

    public void setUserId(String userId) {
        UserId = userId;
    }

    public String getMemberId() {
        return MemberId;
    }

    public void setMemberId(String memberId) {
        MemberId = memberId;
    }

    public String getCertificateNo() {
        return CertificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        CertificateNo = certificateNo;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public String getActivityCode() {
        return ActivityCode;
    }

    public void setActivityCode(String activityCode) {
        ActivityCode = activityCode;
    }

    public String getActivityNo() {
        return ActivityNo;
    }

    public void setActivityNo(String activityNo) {
        ActivityNo = activityNo;
    }

    public String getActivityName() {
        return ActivityName;
    }

    public void setActivityName(String activityName) {
        ActivityName = activityName;
    }

    public String getPrice() {
        return Price;
    }

    public void setPrice(String price) {
        Price = price;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getOrderStatus() {
        return OrderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        OrderStatus = orderStatus;
    }

    public XMLGregorianCalendar getPayTime() {
        return PayTime;
    }

    public void setPayTime(XMLGregorianCalendar payTime) {
        PayTime = payTime;
    }

    public int getRecordNo() {
        return RecordNo;
    }

    public void setRecordNo(int recordNo) {
        RecordNo = recordNo;
    }

    public String getRecordStatus() {
        return RecordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        RecordStatus = recordStatus;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getPayTimeStr() {
        return this.getPayTime().getYear()+"-"+this.getPayTime().getMonth()+"-"+this.getPayTime().getDay()+" "+fillNumber(String.valueOf(this.getPayTime().getHour()))+":"+fillNumber(String.valueOf(this.getPayTime().getMinute()))+":"+fillNumber(String.valueOf(this.getPayTime().getSecond()));
    }

    public void setPayTimeStr(String payTimeStr) {
        this.payTimeStr = payTimeStr;
    }

    private String fillNumber(String input){
        if(input.length() < 2){
            input = "0"+input;
        }
        return input;
    }
}
