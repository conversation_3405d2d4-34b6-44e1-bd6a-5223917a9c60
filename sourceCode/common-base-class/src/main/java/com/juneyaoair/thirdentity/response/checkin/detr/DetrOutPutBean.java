package com.juneyaoair.thirdentity.response.checkin.detr;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "DetrOutPutBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class DetrOutPutBean {
	private List<DetrTicketBean> tickets;
	private String tipCode;
    private String messageCause;
    
	public List<DetrTicketBean> getTickets() {
		return tickets;
	}

	public void setTickets(List<DetrTicketBean> tickets) {
		this.tickets = tickets;
	}

	public String getTipCode() {
		return tipCode;
	}

	public void setTipCode(String tipCode) {
		this.tipCode = tipCode;
	}

	public String getMessageCause() {
		return messageCause;
	}

	public void setMessageCause(String messageCause) {
		this.messageCause = messageCause;
	}


	
	
}
