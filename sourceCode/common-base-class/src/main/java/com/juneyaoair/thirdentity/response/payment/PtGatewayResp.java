package com.juneyaoair.thirdentity.response.payment;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "PtGatewayResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtGatewayResp {
	private String RespCode;//1001为查询成功；其它为查询失败，失败具体原因见ErrMsg，失败不一定有序号为3的信息。
	private String ErrMsg;//错误信息
	private String ChannelNo;//渠道用户号B2C,CC等
	private PtGatewayInfo[] GatewayInfoList;//渠道可用支付网关列表


	public PtGatewayResp() {
		super();
	}


	public String getRespCode() {
		return RespCode;
	}


	public void setRespCode(String respCode) {
		RespCode = respCode;
	}


	public String getErrMsg() {
		return ErrMsg;
	}


	public void setErrMsg(String errMsg) {
		ErrMsg = errMsg;
	}


	public String getChannelNo() {
		return ChannelNo;
	}


	public void setChannelNo(String channelNo) {
		ChannelNo = channelNo;
	}


	public PtGatewayInfo[] getGatewayInfoList() {
		return GatewayInfoList;
	}


	public void setGatewayInfoList(PtGatewayInfo[] gatewayInfoList) {
		GatewayInfoList = gatewayInfoList;
	}
	
}