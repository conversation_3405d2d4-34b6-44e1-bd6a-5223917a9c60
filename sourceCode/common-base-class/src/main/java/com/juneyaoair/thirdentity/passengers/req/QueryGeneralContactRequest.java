package com.juneyaoair.thirdentity.passengers.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 查询乘机人信息V2
 * @date 2019/7/18  17:38.
 */
@Data
@NoArgsConstructor
public class QueryGeneralContactRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ChannelCustomerNo;
    private String ChannelCustomerType;//CRM - 常客,Other - 其它
    private String SortType;
    private String PassengerName;
    private String PassEnNameS;
    private String PassEnNameF;
    private String CertNo;
    private String DepCity;
    private String ArrCity;
    private String IsGMJC;  //Y:军警残购票，N/null:正常购票
    private List<String> ContactType;// A=儿童畅飞，B=成人畅飞
    private int PageNo;
    private int PageSize;

    public QueryGeneralContactRequest(String version,String channelCode,String userNo,String channelCustomerType,String sortType){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.ChannelCustomerType = channelCustomerType;
        this.SortType = sortType;
    }
}
