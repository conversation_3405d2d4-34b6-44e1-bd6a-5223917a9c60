package com.juneyaoair.thirdentity.response.question;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/4/9  9:51.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtGetSurveyAndQuestionsResponse {
    @SerializedName(value = "SurveyBaseInfoEntity")
    private SurveyBaseInfo surveyBaseInfoEntity;//问卷基本信息
    private List<Group> GroupEntitys;//问卷对应问题
    private String memberName;//会员名
    private String scoreNum;//会员积分
    private boolean complete;//是否全部做答
    private boolean takeScore;//是否已领积分
}
