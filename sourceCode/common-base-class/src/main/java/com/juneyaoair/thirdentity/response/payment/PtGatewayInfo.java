package com.juneyaoair.thirdentity.response.payment;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "PtGatewayInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtGatewayInfo {
	private String GatewayNo;//支付网关号,在业务系统中选择使用的支付网关。
	private String GatewayName;//支付网关名称
	private String GatewayLogoUrl;//支付网关Logo URL
	private String GatewayLimitUrl;//支付网关支付限制说明URL,支付限额说明（URL）
	private String GatewayRemark;//支付网关备注
	private String GatewayType;//支付网关类型,1 － 直联银行，2 － 第三方网关，3 －预付费卡,4 – 无磁无密
	private String GatewayProvider;//支付网关服务商,支付平台支付网关所属的第三支付网关，如：汇付兴业属于汇付网关，银联兴业属于银联网关
	private String 	GatewayProviderName;//支付网关服务商名称
	private String ShowSort;//显示顺序,数字越小显示越靠前


	public PtGatewayInfo() {
		super();
	}


	public String getGatewayNo() {
		return GatewayNo;
	}


	public void setGatewayNo(String gatewayNo) {
		GatewayNo = gatewayNo;
	}


	public String getGatewayName() {
		return GatewayName;
	}


	public void setGatewayName(String gatewayName) {
		GatewayName = gatewayName;
	}


	public String getGatewayLogoUrl() {
		return GatewayLogoUrl;
	}


	public void setGatewayLogoUrl(String gatewayLogoUrl) {
		GatewayLogoUrl = gatewayLogoUrl;
	}


	public String getGatewayLimitUrl() {
		return GatewayLimitUrl;
	}


	public void setGatewayLimitUrl(String gatewayLimitUrl) {
		GatewayLimitUrl = gatewayLimitUrl;
	}


	public String getGatewayRemark() {
		return GatewayRemark;
	}


	public void setGatewayRemark(String gatewayRemark) {
		GatewayRemark = gatewayRemark;
	}


	public String getGatewayType() {
		return GatewayType;
	}


	public void setGatewayType(String gatewayType) {
		GatewayType = gatewayType;
	}


	public String getGatewayProvider() {
		return GatewayProvider;
	}


	public void setGatewayProvider(String gatewayProvider) {
		GatewayProvider = gatewayProvider;
	}


	public String getGatewayProviderName() {
		return GatewayProviderName;
	}


	public void setGatewayProviderName(String gatewayProviderName) {
		GatewayProviderName = gatewayProviderName;
	}


	public String getShowSort() {
		return ShowSort;
	}


	public void setShowSort(String showSort) {
		ShowSort = showSort;
	}
	
	
}