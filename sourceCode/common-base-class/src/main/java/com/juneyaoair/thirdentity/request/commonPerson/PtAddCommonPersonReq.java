package com.juneyaoair.thirdentity.request.commonPerson;

import com.juneyaoair.thirdentity.response.commonPerson.PtCommonPersonInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PtModifyCommonPersonReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtAddCommonPersonReq {
	
	private String Version; // 接口版本号10
	private String ChannelCode; // 渠道用户号B2C,CC等
	private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
	
	private PtCommonPersonInfo[] CommonPersonInfoList; // 常用旅客联系信息列表
	
	
	public PtAddCommonPersonReq() {
		super();
	}
	
	public PtAddCommonPersonReq(String version,
			String userNo, String channelCode) {
		super();
		Version = version;
		ChannelCode = channelCode;
		UserNo = userNo;
	}
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}

	public PtCommonPersonInfo[] getCommonPersonInfoList() {
		return CommonPersonInfoList;
	}

	public void setCommonPersonInfoList(PtCommonPersonInfo[] commonPersonInfoList) {
		CommonPersonInfoList = commonPersonInfoList;
	}



	
}