package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "CheckInStatus")
@XmlAccessorType(XmlAccessType.FIELD)
public class PWResult {
	private  String tipCode ;
	private String  messageCause ;
	public String getTipCode() {
		return tipCode;
	}
	public void setTipCode(String tipCode) {
		this.tipCode = tipCode;
	}
	public String getMessageCause() {
		return messageCause;
	}
	public void setMessageCause(String messageCause) {
		this.messageCause = messageCause;
	}
	
	
	

}
