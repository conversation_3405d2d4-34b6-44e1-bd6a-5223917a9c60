package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/7/29.
 */
@XmlRootElement(name = "CheckInTicket")
@XmlAccessorType(XmlAccessType.FIELD)
public class CheckInTicket {
    private String TicketNo;//票号
    private String DepAirport;//出发机场
    private String ArrAirport;//到达机场
    private String DepDateTime;//出发时间
    private String PassengerName;//乘客姓名
    private String DepTerm;//出发航站楼
    private String ArrTerm;//到达航站楼
    private String FlightNo;//航班号
    private String PlaneStyle;//机型

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getDepDateTime() {
        return DepDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        DepDateTime = depDateTime;
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }

    public String getDepTerm() {
        return DepTerm;
    }

    public void setDepTerm(String depTerm) {
        DepTerm = depTerm;
    }

    public String getArrTerm() {
        return ArrTerm;
    }

    public void setArrTerm(String arrTerm) {
        ArrTerm = arrTerm;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getPlaneStyle() {
        return PlaneStyle;
    }

    public void setPlaneStyle(String planeStyle) {
        PlaneStyle = planeStyle;
    }
}
