package com.juneyaoair.thirdentity.request.speedRefund;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2017/1/18.
 */
@XmlRootElement(name = "QueryTicketPayoutRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryTicketPayoutRequest {
    private String Version; //接口版本号
    private String ChannelCode;//渠道用户号
    private String UserNo;//渠道工作人员号
    private String ChannelCustomerNo;//渠道客户编号
    private String CreateDateBegin;//退票创建起始日期
    private String CreateDateEnd;//退票创建结束日期  格式:yyyy-MM-dd
    private int PageNo;  //页码
    private int PageSize;  //每页大小  5~50条之间

    public QueryTicketPayoutRequest(String version, String channelCode, String userNo, int pageSize, int pageNo,
                                    String createDateEnd, String createDateBegin) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        PageSize = pageSize;
        PageNo = pageNo;
        CreateDateEnd = createDateEnd;
        CreateDateBegin = createDateBegin;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getChannelCustomerNo() {
        return ChannelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        ChannelCustomerNo = channelCustomerNo;
    }

    public String getCreateDateBegin() {
        return CreateDateBegin;
    }

    public void setCreateDateBegin(String createDateBegin) {
        CreateDateBegin = createDateBegin;
    }

    public String getCreateDateEnd() {
        return CreateDateEnd;
    }

    public void setCreateDateEnd(String createDateEnd) {
        CreateDateEnd = createDateEnd;
    }

    public int getPageNo() {
        return PageNo;
    }

    public void setPageNo(int pageNo) {
        PageNo = pageNo;
    }

    public int getPageSize() {
        return PageSize;
    }

    public void setPageSize(int pageSize) {
        PageSize = pageSize;
    }
}
