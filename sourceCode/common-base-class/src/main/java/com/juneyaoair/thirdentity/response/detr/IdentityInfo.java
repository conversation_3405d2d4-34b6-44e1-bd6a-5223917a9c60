package com.juneyaoair.thirdentity.response.detr;

public class IdentityInfo {
	private String IdNo; //证件号DETR:,F提取到的信息，有证件信息、行程单和常客等等信息都在这里
	private String IdType; //证件类型

	public String Birthdate;//仅有DOCS证件返回 出生日期
	public String Sex;//同上 性别
	public String Nationality;//同上 国籍
	public String BelongCountry;//同上 证件签发国
	public String CertValidity;//同上 证件有效期

	public String getIdNo(){
		return IdNo;
	}
	public void setIdNo(String IdNo){
		this.IdNo=IdNo;
	}
	public String getIdType(){
		return IdType;
	}
	public void setIdType(String IdType){
		this.IdType=IdType;
	}

	public String getBirthdate() {
		return Birthdate;
	}

	public void setBirthdate(String birthdate) {
		Birthdate = birthdate;
	}

	public String getSex() {
		return Sex;
	}

	public void setSex(String sex) {
		Sex = sex;
	}

	public String getNationality() {
		return Nationality;
	}

	public void setNationality(String nationality) {
		Nationality = nationality;
	}

	public String getBelongCountry() {
		return BelongCountry;
	}

	public void setBelongCountry(String belongCountry) {
		BelongCountry = belongCountry;
	}

	public String getCertValidity() {
		return CertValidity;
	}

	public void setCertValidity(String certValidity) {
		CertValidity = certValidity;
	}

	@Override
	public String toString() {
		return "IdentityInfo [" +
				"IdNo='" + IdNo + '\'' +
				", IdType='" + IdType + '\'' +
				", Birthdate=" + Birthdate +
				", Sex='" + Sex + '\'' +
				", Nationality='" + Nationality + '\'' +
				", BelongCountry='" + BelongCountry + '\'' +
				", CertValidity=" + CertValidity +
				']';
	}
}