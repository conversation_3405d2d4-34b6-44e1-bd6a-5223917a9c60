package com.juneyaoair.thirdentity.response.suggestion;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PriSugComB2CResponse
 * @Description  表扬,投诉,意见新增接口 返回结果
 * <AUTHOR>
 * @Date 2019/4/15 15:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PriSugComB2CResponse {

    /**
     * 接口调用：0成功，1失败
     */
    private int code = 1;

    /**
     * 操作成功、操作失败等描述性信息
     */
    private String msg;

    /**
     * true:新增成功
     * false:新增失败
     */
    private Boolean data;

}
