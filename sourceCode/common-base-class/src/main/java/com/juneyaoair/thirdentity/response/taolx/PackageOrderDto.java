package com.juneyaoair.thirdentity.response.taolx;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * Created by yaocf on 2017/7/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderDto {
    private OrderProductDto Product;//产品信息
    private List<OrderCustomerDto> Customers;//客人用户信息
    private Integer ID;//订单主键
    private String OrderNo;//订单号
    private String TaolxOrderNo;//淘旅行订单号
    private Double TotalPrice;//订单总价(原价)
    private Integer AdultCount;//成人数
    private Integer ChildCount;//儿童数
    private Integer InfCount;//婴儿数
    private Double TotalDiscount;//总优惠金额
    private Double PaymentPrice;//应付金额(总价-优惠金额)
    private Double PaidPrice;//已付金额
    private Integer OrderStatus;//订单状态 (-10: 创单中 -11:创单失败 -12:淘旅行创单失败 9:创单成功 10:已提交 11:核实中 20:确认中 30:确认完成 40:已成交 50：已取消 60：退订中 70：全部退订 80：部分退订)
    private Integer PayStatus;//支付状态（1：未支付 2：已支付 3:无需支付）
    private String Linker;//主单联系人姓名
    private String LinkerMobile;//主单联系人电话
    private String LinkerMail;//主单联系人邮箱
    private String TimeLimit;//订单时限
    private Integer OrderType;//订单类型（1：酒店 2：度假）
    private String CreateTime;//创建时间
    private Integer UserID;//创建者ID
    private String IsAllowPay;//是否可以支付，（数据库状态为未提交，订单未超时，支付状态为未支付）
    private String IsAllowCancel;//是否允许取消，（数据库为已提交，）
}
