package com.juneyaoair.thirdentity.response.reservation;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName PtQueryCancelPnrRecordInfoResp
 * <AUTHOR>
 * @Description
 * @Date 2021-08-04 09:52
 **/
@XmlRootElement(name = "PtQueryCancelPnrRecordInfoResp")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PtCancelPnrInfoResp {
    private String Version; //版本号
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String ResultCode;  //结果代码
    private String ErrorInfo;   //错误信息

}
