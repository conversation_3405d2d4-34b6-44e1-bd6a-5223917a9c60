package com.juneyaoair.thirdentity.response.order.apply;

import io.swagger.annotations.ApiModelProperty;

public class PtTaxInfo {
	private String TaxCode; //税费代码
	@ApiModelProperty(value = "税费名称")
	private String TaxName;
	private String Currency; //币种
	private double TaxAmount; //税费金额
	private String TaxSuffix; //税费后缀信息
	private int TaxSeq; //税费顺序号
	public String getTaxCode() {
		return TaxCode;
	}
	public void setTaxCode(String taxCode) {
		TaxCode = taxCode;
	}
	public String getCurrency() {
		return Currency;
	}
	public void setCurrency(String currency) {
		Currency = currency;
	}
	public double getTaxAmount() {
		return TaxAmount;
	}
	public void setTaxAmount(double taxAmount) {
		TaxAmount = taxAmount;
	}
	public String getTaxSuffix() {
		return TaxSuffix;
	}
	public void setTaxSuffix(String taxSuffix) {
		TaxSuffix = taxSuffix;
	}
	public int getTaxSeq() {
		return TaxSeq;
	}
	public void setTaxSeq(int taxSeq) {
		TaxSeq = taxSeq;
	}

	public String getTaxName() {
		return TaxName;
	}

	public void setTaxName(String taxName) {
		TaxName = taxName;
	}
}