package com.juneyaoair.thirdentity.response.question;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/4/18  18:08.
 * 问卷公共的返回结构体
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtUserSurveyResponse<T> {
    @SerializedName("Data")
    private T data;
    @SerializedName("IsSuccess")
    private Boolean isSuccess;//是否执行成功
    @SerializedName("Code")
    private int code;//代码100:成功 200:没有数据 300:请求失败 500:请求参数异常 600:程序异常
    @SerializedName("ZhMsg")
    private String zhMsg;//中文提示
}
