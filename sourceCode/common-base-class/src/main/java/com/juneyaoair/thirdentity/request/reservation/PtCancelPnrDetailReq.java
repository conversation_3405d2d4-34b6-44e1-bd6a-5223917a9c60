package com.juneyaoair.thirdentity.request.reservation;

import lombok.Data;

/**
 * @ClassName PtCancelPnrDetailReq
 * <AUTHOR>
 * @Description 查询取消pnr详情
 * @Date 2021-08-23 16:02
 **/
@Data
public class PtCancelPnrDetailReq {
    private String Version; // 接口版本号
    private String ChannelCode; // 渠道用户号
    private String UserNo; // 渠道工作人员号
    private String ChannelCustomerNo; //渠道客户编号 ffpId
    private String TicketNo; // 电子客票号
    private String FfpId; // 会员id
    private String FfCardNo; // 会员卡号
    private int CancelTicketSeatId;//取消记录id

    public PtCancelPnrDetailReq(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }
}

