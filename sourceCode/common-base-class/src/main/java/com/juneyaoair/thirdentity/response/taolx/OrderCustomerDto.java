package com.juneyaoair.thirdentity.response.taolx;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2017/7/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCustomerDto {
    private Integer ID;//客人信息主键
    private Integer OrderID;//房间信息主键
    private String OrderNo;//订单号
    private String CnName;//中文名
    private String EnName;//英文名
    private String IdType;//证件类型
    private String IdValue;//证件号
    private Integer TypeExt;//乘客类型 成人:1 儿童:2 婴儿:3
}
