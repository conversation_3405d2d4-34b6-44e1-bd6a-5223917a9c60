package com.juneyaoair.thirdentity.tongdun.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/1/16 15:12
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class FraudApiResponse {
    private static final long     serialVersionUID  = 4152462611121573434L;
    private Boolean               success           = false;    			// 执行是否成功，不成功时对应reason_code
    private String                reason_code;             				    // 错误码及原因描述，正常执行完扫描时为空
    private Integer 			  final_score; 			  			        // 风险分数
    private String    			  final_decision; 		  				    // 最终的风险决策结果
    private String 				  policy_name;			  				    // 策略名称
    private List<HitRule> hit_rules         = new ArrayList<>();	// 命中规则列表
    private String 				  seq_id; 				  				    // 请求序列号，每个请求进来都分配一个全局唯一的id
    private Integer 			  spend_time; 			  			        // 花费的时间，单位ms
    private Map<String, String>   geoip_info        = new HashMap<>(); 	    // 地理位置信息
    private Map<String, Object> device_info       = new HashMap<>(); 	    // 设备指纹信息
    private Map<String, Object>   attribution       = new HashMap<>();      // 归属地信息
    private List<Policy> 	      policy_set        = new ArrayList<>();    // 策略集信息
    private String 				  policy_set_name;	 	  				    // 策略集名称
    private String 				  risk_type; 				  			    // 风险类型
    /**
     * 虚拟IP
     */
    private String virtualIp;
    /**
     * 原因描述
     */
    private String reason_desc;
    /**
     * 备注说明
     */
    private String remark;

    @Override
    public String toString() {
        return "FraudApiResponse [success=" + success + ", reason_code=" + reason_code + ", final_score=" + final_score
                + ", final_decision=" + final_decision + ", policy_name=" + policy_name + ", seq_id=" + seq_id
                + ", spend_time=" + spend_time + ", policy_set_name=" + policy_set_name + ", risk_type=" + risk_type
                + "]";
    }
}
