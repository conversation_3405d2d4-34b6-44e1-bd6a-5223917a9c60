package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "HOOpenTimeResultBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class HOOpenTimeResultBean {
	private String deptCity; //起飞机场名
	private String deptCityNm; 
	private String marks; //标注释义说明 或录入者
	private String openFre;
	private String openEnd;
	
	public String getDeptCity() {
		return deptCity;
	}
	public void setDeptCity(String deptCity) {
		this.deptCity = deptCity;
	}
	public String getDeptCityNm() {
		return deptCityNm;
	}
	public void setDeptCityNm(String deptCityNm) {
		this.deptCityNm = deptCityNm;
	}
	public String getMarks() {
		return marks;
	}
	public void setMarks(String marks) {
		this.marks = marks;
	}
	public String getOpenFre() {
		return openFre;
	}
	public void setOpenFre(String openFre) {
		this.openFre = openFre;
	}
	public String getOpenEnd() {
		return openEnd;
	}
	public void setOpenEnd(String openEnd) {
		this.openEnd = openEnd;
	}
}
