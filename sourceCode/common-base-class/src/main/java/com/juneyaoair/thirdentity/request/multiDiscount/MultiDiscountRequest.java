package com.juneyaoair.thirdentity.request.multiDiscount;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:14 2017/10/18
 * @Modified by:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiDiscountRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private int PassengerCount;//乘客数
    private int TicketAmount;//机票金额
    private String AirlineType;//国际国内标识  D国内  I国际
    private String RouteType;//航程类型  OW单程  RT往返
    private String Cabin;//来回程舱位，以英文逗号分隔，例如M,L
    private String FlightDateGo;//出发日期
    private String FlightDateBack;//返回日期

    public MultiDiscountRequest(String version,String channelCode,String userNo,int passengerCount,int ticketAmount,String airlineType,String flightDateGo,String flightDateBack){
        this.Version =version;
        this.ChannelCode =channelCode;
        this.UserNo =userNo;
        this.PassengerCount =passengerCount;
        this.TicketAmount =ticketAmount;
        this.AirlineType =airlineType;
        this.FlightDateGo =flightDateGo;
        this.FlightDateBack =flightDateBack;
    }
}
