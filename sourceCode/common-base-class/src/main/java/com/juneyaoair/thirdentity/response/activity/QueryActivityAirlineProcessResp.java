package com.juneyaoair.thirdentity.response.activity;

import com.juneyaoair.thirdentity.request.activity.ActivityAirline;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by zc on 2016/10/28.
 */
@XmlRootElement(name = "QueryActivityAirlineProcessResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryActivityAirlineProcessResp {

    private String ResultCode;
    private String ErrorInfo;
    private List<ActivityAirline> ActivityAirlineAList;
    private List<ActivityAirline> ActivityAirlineBList;
    private List<ActivityAirline> ActivityAirlineCList;
    private List<ActivityAirline> ActivityAirlineDList;
    private List<ActivityAirline> ActivityAirlineEList;

    private String currentActState;

    private long secondsCountDown;

    public QueryActivityAirlineProcessResp() {
        super();
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public List<ActivityAirline> getActivityAirlineAList() {
        return ActivityAirlineAList;
    }

    public void setActivityAirlineAList(List<ActivityAirline> activityAirlineAList) {
        ActivityAirlineAList = activityAirlineAList;
    }

    public List<ActivityAirline> getActivityAirlineBList() {
        return ActivityAirlineBList;
    }

    public void setActivityAirlineBList(List<ActivityAirline> activityAirlineBList) {
        ActivityAirlineBList = activityAirlineBList;
    }

    public List<ActivityAirline> getActivityAirlineCList() {
        return ActivityAirlineCList;
    }

    public void setActivityAirlineCList(List<ActivityAirline> activityAirlineCList) {
        ActivityAirlineCList = activityAirlineCList;
    }

    public List<ActivityAirline> getActivityAirlineDList() {
        return ActivityAirlineDList;
    }

    public void setActivityAirlineDList(List<ActivityAirline> activityAirlineDList) {
        ActivityAirlineDList = activityAirlineDList;
    }

    public List<ActivityAirline> getActivityAirlineEList() {
        return ActivityAirlineEList;
    }

    public void setActivityAirlineEList(List<ActivityAirline> activityAirlineEList) {
        ActivityAirlineEList = activityAirlineEList;
    }

    public String getCurrentActState() {
        return currentActState;
    }

    public void setCurrentActState(String currentActState) {
        this.currentActState = currentActState;
    }

    public long getSecondsCountDown() {
        return secondsCountDown;
    }

    public void setSecondsCountDown(long secondsCountDown) {
        this.secondsCountDown = secondsCountDown;
    }
}
