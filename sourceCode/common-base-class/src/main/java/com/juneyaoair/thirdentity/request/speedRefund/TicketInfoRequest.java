package com.juneyaoair.thirdentity.request.speedRefund;

import lombok.Data;

/**
 * Created by yaocf on 2017/1/16.
 * 客票信息查询请求类
 */
@Data
public class TicketInfoRequest {
    /**
     * 接口版本号
     */
    private String Version;
    /**
     * 渠道用户号
     */
    private String ChannelCode;
    /**
     * 渠道工作人员
     */
    private String UserNo;
    /**
     * 电子客票号
     */
    private String TicketNo;
    /**
     * 证件号描述
     */
    private String CertNo;
    /**
     * 证件类型
     */
    private String CertType;
    /**
     * 查询类型，是否可升舱或改期
     * UPGRADE升舱，CHANGE改期，默认空兼容之前查询，不处理
     * @see com.juneyaoair.appenum.ticket.TicketQueryTypeEnum
     */
    private String QueryType;
    /**
     * 旅客姓名
     **/
    private String PassengerName;

    public TicketInfoRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }
}
