package com.juneyaoair.thirdentity.request.BaggageExcess;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:00 2018/1/7
 * @Modified by:
 */
@XmlRootElement(name = "QueryBaggageExcessSegmentInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryBaggageExcessSegmentInfo {
    private int SegNO; //旅行顺序第一段为从0开始，第二段为1，依次增加
    private String DepAirport;
    private String ArrAirport;
    private String DepAirName;
    private String ArrAirName;
    private String DepDateTime;
    private String Cabin;
    private String FlightNo;
    private List<BaggageExcess> BaggageExcessList;

    public QueryBaggageExcessSegmentInfo() {
    }

    public QueryBaggageExcessSegmentInfo(int segNO, String depAirport, String arrAirport, String depAirName, String arrAirName, String depDateTime, String cabin, String flightNo, List<BaggageExcess> baggageExcessList) {
        SegNO = segNO;
        DepAirport = depAirport;
        ArrAirport = arrAirport;
        DepAirName = depAirName;
        ArrAirName = arrAirName;
        DepDateTime = depDateTime;
        Cabin = cabin;
        FlightNo = flightNo;
        BaggageExcessList = baggageExcessList;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getDepAirName() {
        return DepAirName;
    }

    public void setDepAirName(String depAirName) {
        DepAirName = depAirName;
    }

    public String getArrAirName() {
        return ArrAirName;
    }

    public void setArrAirName(String arrAirName) {
        ArrAirName = arrAirName;
    }

    public int getSegNO() {
        return SegNO;
    }

    public void setSegNO(int segNO) {
        this.SegNO = segNO;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getDepDateTime() {
        return DepDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        DepDateTime = depDateTime;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public List<BaggageExcess> getBaggageExcessList() {
        return BaggageExcessList;
    }

    public void setBaggageExcessList(List<BaggageExcess> baggageExcessList) {
        BaggageExcessList = baggageExcessList;
    }
}
