package com.juneyaoair.thirdentity.response.order.apply;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 订单详情行李信息
 * <AUTHOR>
 * @Description
 * @create 2020-06-19 13:38
 */
@Data
public class OrderDetailBaggage {
    /**
     * 手提行李
     */
    @SerializedName(value = "HandBaggeage")
    private String handBaggeage;
    /**
     * 手提行李说明
     */
    @SerializedName(value = "HandBaggeageRemark")
    private String handBaggeageRemark;
    /**
     * 手提行李件数
     */
    private String countLimit;
    /**
     * 托运行李
     */
    @SerializedName(value = "CheckBaggeage")
    private String checkBaggeage;
    /**
     * 托运行李说明
     */
    @SerializedName(value = "CheckBaggeageRemark")
    private String checkBaggeageRemark;
    /**
     * 特殊说明
     */
    @SerializedName(value = "SpecialRemark")
    private String specialRemark;
}
