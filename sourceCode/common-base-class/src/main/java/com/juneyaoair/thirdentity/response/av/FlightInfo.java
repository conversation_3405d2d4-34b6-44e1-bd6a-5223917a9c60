package com.juneyaoair.thirdentity.response.av;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.util.Arrays;

@XmlRootElement(name = "FlightInfo")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ID","FlightNo","DepDateTime","ArrDateTime","CodeShare","CarrierNo","FType","MealCode","ASR","StopNumber","DepTerm","ArrTerm","ETkt","CabinFareList",
					"StopAirport","StopArrTime","StopDepTime","DepZone","ArrZone","VirtualFlights"})
public class FlightInfo {
	
	private	String	ID	;	//	ID	标识唯一一个航班，由航段序号+航段机场序号+航班号序号 如:0_0_0
	private	String	FlightNo	;	//	航班号	HO1221
	private	String	DepDateTime	;	//	起飞日期时间	yyyy-MM-dd hh:mm
	private	String	ArrDateTime	;	//	到达日期时间	yyyy-MM-dd hh:mm
	private	boolean	CodeShare	;	//	是否共享航班	
	private	String	CarrierNo	;	//	实际承运航班号	
	private	String	FType	;	//	机型	
	private	String	MealCode	;	//	餐食代码	
	private	boolean	ASR	;	//	是否可机上订位	
	private	String	StopNumber	;	//	经停次数	
	private	String	DepTerm	;	//	起飞航站楼	
	private	String	ArrTerm	;	//	到达航站楼	
	private	boolean	ETkt	;	//	是否支持电子客票	
	private	CabinFareApi[]	CabinFareList	;	//	舱位运价列表
	private String StopAirport;
	private String StopArrTime;
	private String StopDepTime;
	private String DepZone;
	private String ArrZone;
	private String VirtualFlights;
	private int StopDuration;//经停时间  单位：分钟
	/**
	 * 取得指定舱位的WIFI信息 the nwst：机上网络服务标识 ，
	 * I，代表该航班拥有客舱互联网络
	 * W，代表该航班拥有客舱WIFI局域网络，没有客舱互联网
	 * V，代表该航班拥有全舱AVOD，没有客舱互联网和WIFI
	 */
	private String Nwst;
	
	public String getID() {
		return ID;
	}
	public void setID(String iD) {
		ID = iD;
	}
	public String getFlightNo() {
		return FlightNo;
	}
	public void setFlightNo(String flightNo) {
		FlightNo = flightNo;
	}
	public String getDepDateTime() {
		return DepDateTime;
	}
	public void setDepDateTime(String depDateTime) {
		DepDateTime = depDateTime;
	}
	public String getArrDateTime() {
		return ArrDateTime;
	}
	public void setArrDateTime(String arrDateTime) {
		ArrDateTime = arrDateTime;
	}
	public boolean getCodeShare() {
		return CodeShare;
	}
	public void setCodeShare(boolean codeShare) {
		CodeShare = codeShare;
	}
	public String getCarrierNo() {
		return CarrierNo;
	}
	public void setCarrierNo(String carrierNo) {
		CarrierNo = carrierNo;
	}
	public String getFType() {
		return FType;
	}
	public void setFType(String fType) {
		FType = fType;
	}
	public String getMealCode() {
		return MealCode;
	}
	public void setMealCode(String mealCode) {
		MealCode = mealCode;
	}
	public boolean getASR() {
		return ASR;
	}
	public void setASR(boolean aSR) {
		ASR = aSR;
	}
	public String getStopNumber() {
		return StopNumber;
	}
	public void setStopNumber(String stopNumber) {
		StopNumber = stopNumber;
	}
	public String getDepTerm() {
		return DepTerm;
	}
	public void setDepTerm(String depTerm) {
		DepTerm = depTerm;
	}
	public String getArrTerm() {
		return ArrTerm;
	}
	public void setArrTerm(String arrTerm) {
		ArrTerm = arrTerm;
	}
	public boolean getETkt() {
		return ETkt;
	}
	public void setETkt(boolean eTkt) {
		ETkt = eTkt;
	}
	public CabinFareApi[] getCabinFareList() {
		return CabinFareList;
	}
	public void setCabinFareList(CabinFareApi[] cabinFareList) {
		CabinFareList = cabinFareList;
	}

	public boolean isCodeShare() {
		return CodeShare;
	}

	public boolean isASR() {
		return ASR;
	}

	public boolean isETkt() {
		return ETkt;
	}

	public String getStopAirport() {
		return StopAirport;
	}

	public void setStopAirport(String stopAirport) {
		StopAirport = stopAirport;
	}

	public String getStopArrTime() {
		return StopArrTime;
	}

	public void setStopArrTime(String stopArrTime) {
		StopArrTime = stopArrTime;
	}

	public String getStopDepTime() {
		return StopDepTime;
	}

	public void setStopDepTime(String stopDepTime) {
		StopDepTime = stopDepTime;
	}

	public String getDepZone() {
		return DepZone;
	}

	public void setDepZone(String depZone) {
		DepZone = depZone;
	}

	public String getArrZone() {
		return ArrZone;
	}

	public void setArrZone(String arrZone) {
		ArrZone = arrZone;
	}

	public String getVirtualFlights() {
		return VirtualFlights;
	}

	public void setVirtualFlights(String virtualFlights) {
		VirtualFlights = virtualFlights;
	}

	public int getStopDuration() {
		return StopDuration;
	}

	public void setStopDuration(int stopDuration) {
		StopDuration = stopDuration;
	}

	public String getNwst() {
		return Nwst;
	}

	public void setNwst(String nwst) {
		Nwst = nwst;
	}

	@Override
	public String toString() {
		return "FlightInfo [ID=" + ID + ", FlightNo=" + FlightNo
				+ ", DepDateTime=" + DepDateTime + ", ArrDateTime="
				+ ArrDateTime + ", CodeShare=" + CodeShare + ", CarrierNo="
				+ CarrierNo + ", FType=" + FType + ", MealCode=" + MealCode
				+ ", ASR=" + ASR + ", StopNumber=" + StopNumber + ", DepTerm="
				+ DepTerm + ", ArrTerm=" + ArrTerm + ", ETkt=" + ETkt
				+ ", CabinFareList=" + Arrays.toString(CabinFareList) + "]";
	}
	
	
}
