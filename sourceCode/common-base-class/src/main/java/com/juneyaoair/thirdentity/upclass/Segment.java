package com.juneyaoair.thirdentity.upclass;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/14  16:35.
 */
@Data
public class Segment {
    /**
     * 旅行顺序  第一段为从0开始，第二段为1，依次增加
     */
    private int SegNO;
    /**
     * 飞行方向  去程为G,回程为B
     */
    private String FlightDirection;
    /**
     * 起始地城市三字码
     */
    private String DepCity;
    /**
     * 目的地城市三字码
     */
    private String ArrCity;
    /**
     * 航班日期  yyyy-MM-dd
     */
    private String FlightDate;
}
