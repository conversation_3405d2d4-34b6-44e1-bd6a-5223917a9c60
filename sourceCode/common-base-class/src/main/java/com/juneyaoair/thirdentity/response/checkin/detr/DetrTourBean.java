package com.juneyaoair.thirdentity.response.checkin.detr;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "DetrTourBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class DetrTourBean {
	private String tourIndex;
	private String fromCity;
	private String passByCity;
	private String toCity;
	private String pnr;
	private String airlineCode;
	private String flightNumber;
	private String tourDate;
	private String tourTime;
	private String tourClass;
	private String status;
	private String carrAirlineCode;

	public String getTourIndex() {
		return tourIndex;
	}
	public void setTourIndex(String tourIndex) {
		this.tourIndex = tourIndex;
	}
	public String getFromCity() {
		return fromCity;
	}
	public void setFromCity(String fromCity) {
		this.fromCity = fromCity;
	}
	public String getPassByCity() {
		return passByCity;
	}
	public void setPassByCity(String passByCity) {
		this.passByCity = passByCity;
	}
	public String getToCity() {
		return toCity;
	}
	public void setToCity(String toCity) {
		this.toCity = toCity;
	}
	public String getPnr() {
		return pnr;
	}
	public void setPnr(String pnr) {
		this.pnr = pnr;
	}
	public String getAirlineCode() {
		return airlineCode;
	}
	public void setAirlineCode(String airlineCode) {
		this.airlineCode = airlineCode;
	}
	public String getFlightNumber() {
		return flightNumber;
	}
	public void setFlightNumber(String flightNumber) {
		this.flightNumber = flightNumber;
	}
	public String getTourDate() {
		return tourDate;
	}
	public void setTourDate(String tourDate) {
		this.tourDate = tourDate;
	}
	public String getTourTime() {
		return tourTime;
	}
	public void setTourTime(String tourTime) {
		this.tourTime = tourTime;
	}
	public String getTourClass() {
		return tourClass;
	}
	public void setTourClass(String tourClass) {
		this.tourClass = tourClass;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCarrAirlineCode() {
		return carrAirlineCode;
	}
	public void setCarrAirlineCode(String carrAirlineCode) {
		this.carrAirlineCode = carrAirlineCode;
	}


}
