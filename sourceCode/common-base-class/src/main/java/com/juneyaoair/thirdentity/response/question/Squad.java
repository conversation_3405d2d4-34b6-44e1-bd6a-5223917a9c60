package com.juneyaoair.thirdentity.response.question;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/4/9  10:23.
 * 所属小分组
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Squad {
    private Integer ID;//主键
    private String Title;//小组标题
    private String Description;//小组描述
    private String Remard;//备注
    @SerializedName(value = "Answers")
    private String answers;//问题选择项用###分割
    @SerializedName(value = "AnswersProportion")
    private String answersProportion;//问题选择项权重用###分割
    @SerializedName(value = "QuestionEntitys")
    private List<Question> questionEntitys;//问卷对应问题
}
