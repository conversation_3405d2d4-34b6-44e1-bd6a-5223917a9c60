package com.juneyaoair.thirdentity.request.speedRefund;

import com.juneyaoair.thirdentity.comm.request.PtRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by yaocf on 2018/7/16  10:33.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtBankInfoReq extends PtRequest {
    private String GatewayNo;
    private String BankNo;

    public PtBankInfoReq(String version,String channelCode,String userNo){
        super(version,channelCode,userNo);
    }

    public Map<String,String> getParaMap(){
        Map<String,String> parametersMap = new HashMap<String,String>();
        //1版本号
        parametersMap.put("Version",this.getVersion());
        //2渠道用户
        parametersMap.put("ChannelNo", this.getChannelNo());
        //3网关号
        parametersMap.put("GatewayNo", this.getGatewayNo());
        //4银行卡号
        parametersMap.put("BankNo", this.getBankNo());

        return parametersMap;
    }
}
