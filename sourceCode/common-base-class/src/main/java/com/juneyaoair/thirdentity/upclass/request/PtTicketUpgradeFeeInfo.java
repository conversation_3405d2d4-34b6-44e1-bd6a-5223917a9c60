package com.juneyaoair.thirdentity.upclass.request;

import com.juneyaoair.thirdentity.upclass.Segment;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description  升舱航段信息
 * @date 2018/11/22  19:34.
 */
@Data
public class PtTicketUpgradeFeeInfo {
    /**
     * 航班号
     */
    private String FlightNo;
    /**
     * 乘客类型
     */
    private String PassengerType;
    /**
     * 起始地机场三字码
     */
    private String DepAirport;
    /**
     * 目的地机场三字码
     */
    private String ArrAirport;
    /**
     * 舱位
     */
    private String Cabin;
    /**
     * 票面价
     */
    private Double TicketPrice;
    /**
     * 燃油
     */
    private Double YQTax;
    /**
     * 机建
     */
    private Double CNTax;
    /**
     * 升舱标记
     */
    private boolean UpgradeFlag;
    /**
     * 航班出发时间
     */
    private String DepDateTime;
    /**
     * 航段条件数组
     */
    private List<Segment> SegCondList;
}
