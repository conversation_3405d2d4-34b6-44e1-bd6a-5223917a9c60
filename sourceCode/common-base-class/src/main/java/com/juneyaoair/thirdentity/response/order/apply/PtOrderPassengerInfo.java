package com.juneyaoair.thirdentity.response.order.apply;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;

import java.math.BigDecimal;
import java.util.List;

public class PtOrderPassengerInfo {
	private int PassengerID; //乘客ID	保存在统一订单数据库中的ID
	private int PassengerNO;
	private String PassengerName; //乘客姓名
	private String PassengerType; //乘客类型
	private String Sex; //性别	国际票必填
	private String Birthdate; //出生日期yyyy-MM-dd	儿童、婴儿和国际票必填
	private String Nationality; //国籍	国际票必填
	private String BelongCountry; //发证国	国际票必填
	private String PassengerIdentity; //乘客身份
	private String CertType; //证件类型
	private String CertValidity; //证件有效期yyyy-MM-dd	国际票必填
	private String CertNo; //证件号码
	private String HandphoneNo; //手机号
	private double TicketPrice; //票面价
	private double PricePaid;  //实际支付票面价  当有积分支付时的实际支付票价是Price.PriceValue - 积分抵用金额
	private String FfCardNo; //常客卡号 +++
	private String CountryTelCode; //+++
	private double YQTax; //7(其中2位小数)	燃油费
	private double CNTax; //7(其中2位小数)	建设税
	private double OtherTax; //7(其中2位小数)	其它税费
	private double QFee; //7(其中2位小数)	Q费
	private String AdtNameToInf; //婴儿绑定的成人姓名
	private String IsBuyInsurance; //是否购买保险
	private String IsSaveCommon; //+++
	private double InsuranceAmount; //保险金额	保险金额 = 份数 * 单价 * 航段数
	private List<InsuranceInfo> InsuranceList;
	private Double UpgradeFee;//升舱手续费
	private String RedeemId;
	private String ApprovalCode;

	private String ETicketNo;
	private List<PtSegmentPriceInfo> SegmentPriceInfoList;/// 航段票价信息列表
	private PtTaxInfo[] TaxInfoList; //税费信息
	private String TicketOrderNo;/// 机票订单号
	/// 退票手续费总金额
	/// 应退款 = 票价合计 - 已使用航段票价总金额 - 退票手续费总金额 + 应退票税费合计 + 应退其它费用合计
	private Double DeductionTotal;
	private Double UseFlowdPriceTotal;/// 已使用航段票价总金额
	private Double RefundXTaxTotal;	/// 应退税费合计
	private Double RefundOtherTotal;	/// 应退其它费用合计
	private String RefundSign;/// 退票费验证串 退票信息数组 + 该退票人不含税票价合计 + 退票手续费总金额 + 运价系统退票费计算私钥做SHA1
	/**
	 * 是否使用无限飞
	 * Y/N
	 */
	@SerializedName(value = "UseUnlimitedFlyFlag")
	private String useUnlimitedFlyFlag;
	/**
	 * 无限飞抵扣金额
	 */
	@SerializedName(value = "UseUnlimitedFlyValue")
	private BigDecimal useUnlimitedFlyValue;

	@SerializedName("UseUnlimitedFlyCardNo")
	private String useUnlimitedFlyCardNo;

	/** 是否使用儿童免票券 */
	private String UseFreePass;
	/** 儿童免票券券号 */
	private String UseFreePassCouponNo;

	private double CouponAmount;

	public double getCouponAmount() {
		return CouponAmount;
	}

	public void setCouponAmount(double couponAmount) {
		CouponAmount = couponAmount;
	}

	/**
	 * 单订儿童，成人PNR是否有关联儿童票号，
	 * Y：有关联，N或者空：没有关联
	 */
	private String AdtIsConnectedChd;

	public String getAdtIsConnectedChd() {
		return AdtIsConnectedChd;
	}

	public void setAdtIsConnectedChd(String adtIsConnectedChd) {
		AdtIsConnectedChd = adtIsConnectedChd;
	}

	public int getPassengerID() {
		return PassengerID;
	}
	public void setPassengerID(int passengerID) {
		PassengerID = passengerID;
	}	
	public int getPassengerNO() {
		return PassengerNO;
	}
	public void setPassengerNO(int passengerNO) {
		PassengerNO = passengerNO;
	}
	public String getPassengerName() {
		return PassengerName;
	}
	public void setPassengerName(String passengerName) {
		PassengerName = passengerName;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public String getPassengerIdentity() {
		return PassengerIdentity;
	}
	public void setPassengerIdentity(String passengerIdentity) {
		PassengerIdentity = passengerIdentity;
	}
	public String getCertType() {
		return CertType;
	}
	public void setCertType(String certType) {
		CertType = certType;
	}
	public String getCertNo() {
		return CertNo;
	}
	public void setCertNo(String certNo) {
		CertNo = certNo;
	}
	public String getFfCardNo() {
		return FfCardNo;
	}
	public void setFfCardNo(String ffCardNo) {
		FfCardNo = ffCardNo;
	}
	public String getHandphoneNo() {
		return HandphoneNo;
	}
	public void setHandphoneNo(String handphoneNo) {
		HandphoneNo = handphoneNo;
	}
	public double getYQTax() {
		return YQTax;
	}
	public void setYQTax(double yQTax) {
		YQTax = yQTax;
	}
	public double getCNTax() {
		return CNTax;
	}
	public void setCNTax(double cNTax) {
		CNTax = cNTax;
	}
	public double getOtherTax() {
		return OtherTax;
	}
	public void setOtherTax(double otherTax) {
		OtherTax = otherTax;
	}
	public double getQFee() {
		return QFee;
	}
	public void setQFee(double qFee) {
		QFee = qFee;
	}
	public PtTaxInfo[] getTaxInfoList() {
		return TaxInfoList;
	}
	public void setTaxInfoList(PtTaxInfo[] taxInfoList) {
		TaxInfoList = taxInfoList;
	}
	public double getTicketPrice() {
		return TicketPrice;
	}
	public void setTicketPrice(double ticketPrice) {
		TicketPrice = ticketPrice;
	}	
	public double getPricePaid() {
		return PricePaid;
	}
	public void setPricePaid(double pricePaid) {
		PricePaid = pricePaid;
	}
	public String getAdtNameToInf() {
		return AdtNameToInf;
	}
	public void setAdtNameToInf(String adtNameToInf) {
		AdtNameToInf = adtNameToInf;
	}
	public String getBirthdate() {
		return Birthdate;
	}
	public void setBirthdate(String birthdate) {
		Birthdate = birthdate;
	}
	public String getSex() {
		return Sex;
	}
	public void setSex(String sex) {
		Sex = sex;
	}
	public String getNationality() {
		return Nationality;
	}
	public void setNationality(String nationality) {
		Nationality = nationality;
	}
	public String getBelongCountry() {
		return BelongCountry;
	}
	public void setBelongCountry(String belongCountry) {
		BelongCountry = belongCountry;
	}
	public String getCertValidity() {
		return CertValidity;
	}
	public void setCertValidity(String certValidity) {
		CertValidity = certValidity;
	}
	public String getIsBuyInsurance() {
		return IsBuyInsurance;
	}
	public void setIsBuyInsurance(String isBuyInsurance) {
		IsBuyInsurance = isBuyInsurance;
	}
	public double getInsuranceAmount() {
		return InsuranceAmount;
	}
	public void setInsuranceAmount(double insuranceAmount) {
		InsuranceAmount = insuranceAmount;
	}
	public String getRedeemId() {
		return RedeemId;
	}
	public void setRedeemId(String redeemId) {
		RedeemId = redeemId;
	}
	public String getApprovalCode() {
		return ApprovalCode;
	}
	public void setApprovalCode(String approvalCode) {
		ApprovalCode = approvalCode;
	}

	public String getCountryTelCode() {
		return CountryTelCode;
	}

	public void setCountryTelCode(String countryTelCode) {
		CountryTelCode = countryTelCode;
	}

	public String getIsSaveCommon() {
		return IsSaveCommon;
	}

	public void setIsSaveCommon(String isSaveCommon) {
		IsSaveCommon = isSaveCommon;
	}

	public List<InsuranceInfo> getInsuranceList() {
		return InsuranceList;
	}

	public void setInsuranceList(List<InsuranceInfo> insuranceList) {
		InsuranceList = insuranceList;
	}

	public Double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public String getETicketNo() {
		return ETicketNo;
	}

	public void setETicketNo(String ETicketNo) {
		this.ETicketNo = ETicketNo;
	}

	public List<PtSegmentPriceInfo> getSegmentPriceInfoList() {
		return SegmentPriceInfoList;
	}

	public void setSegmentPriceInfoList(List<PtSegmentPriceInfo> segmentPriceInfoList) {
		SegmentPriceInfoList = segmentPriceInfoList;
	}

	public String getTicketOrderNo() {
		return TicketOrderNo;
	}

	public void setTicketOrderNo(String ticketOrderNo) {
		TicketOrderNo = ticketOrderNo;
	}

	public Double getDeductionTotal() {
		return DeductionTotal;
	}

	public void setDeductionTotal(Double deductionTotal) {
		DeductionTotal = deductionTotal;
	}

	public Double getUseFlowdPriceTotal() {
		return UseFlowdPriceTotal;
	}

	public void setUseFlowdPriceTotal(Double useFlowdPriceTotal) {
		UseFlowdPriceTotal = useFlowdPriceTotal;
	}

	public Double getRefundXTaxTotal() {
		return RefundXTaxTotal;
	}

	public void setRefundXTaxTotal(Double refundXTaxTotal) {
		RefundXTaxTotal = refundXTaxTotal;
	}

	public Double getRefundOtherTotal() {
		return RefundOtherTotal;
	}

	public void setRefundOtherTotal(Double refundOtherTotal) {
		RefundOtherTotal = refundOtherTotal;
	}

	public String getRefundSign() {
		return RefundSign;
	}

	public void setRefundSign(String refundSign) {
		RefundSign = refundSign;
	}

	public String getUseUnlimitedFlyFlag() {
		return useUnlimitedFlyFlag;
	}

	public void setUseUnlimitedFlyFlag(String useUnlimitedFlyFlag) {
		this.useUnlimitedFlyFlag = useUnlimitedFlyFlag;
	}

	public BigDecimal getUseUnlimitedFlyValue() {
		return useUnlimitedFlyValue;
	}

	public void setUseUnlimitedFlyValue(BigDecimal useUnlimitedFlyValue) {
		this.useUnlimitedFlyValue = useUnlimitedFlyValue;
	}

	public String getUseUnlimitedFlyCardNo() {
		return useUnlimitedFlyCardNo;
	}

	public void setUseUnlimitedFlyCardNo(String useUnlimitedFlyCardNo) {
		this.useUnlimitedFlyCardNo = useUnlimitedFlyCardNo;
	}

	public String getUseFreePass() {
		return UseFreePass;
	}

	public void setUseFreePass(String useFreePass) {
		UseFreePass = useFreePass;
	}

	public String getUseFreePassCouponNo() {
		return UseFreePassCouponNo;
	}

	public void setUseFreePassCouponNo(String useFreePassCouponNo) {
		UseFreePassCouponNo = useFreePassCouponNo;
	}
}