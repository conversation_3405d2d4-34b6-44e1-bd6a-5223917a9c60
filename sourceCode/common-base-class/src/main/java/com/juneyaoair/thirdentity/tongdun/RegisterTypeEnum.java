package com.juneyaoair.thirdentity.tongdun;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/1/17 8:50
 */
public enum  RegisterTypeEnum {
    SPEED("SPEED","1"),
    NORMAL("NORMAL","2"),
    ZHIFUBAO("ZHIFUBAO","3"),
    WEIXIN("WEIXIN","4"),
    QQ("QQ","5"),
    APPLE("APPLE", "6");
    private  String code;
    private  String name;
    RegisterTypeEnum(String code,String name){
        this.code = code;
        this.name = name;
    }
    public static RegisterTypeEnum checkEnum(String v){
        for (RegisterTypeEnum c: RegisterTypeEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
