package com.juneyaoair.thirdentity.response.baggageExcess;

import com.juneyaoair.thirdentity.request.BaggageExcess.QueryBaggageExcessSegmentInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 9:44 2018/1/7
 * @Modified by:
 */
@XmlRootElement(name = "QueryBaggageExcessResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryBaggageExcessResponse {
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String Result;
    private List<QueryBaggageExcessSegmentInfo> SegmentInfoList;

    public QueryBaggageExcessResponse() {
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getResult() {
        return Result;
    }

    public void setResult(String result) {
        Result = result;
    }

    public List<QueryBaggageExcessSegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<QueryBaggageExcessSegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }
}
