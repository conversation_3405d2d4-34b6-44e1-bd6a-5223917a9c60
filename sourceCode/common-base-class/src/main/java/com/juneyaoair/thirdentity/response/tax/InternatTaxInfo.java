package com.juneyaoair.thirdentity.response.tax;

public class InternatTaxInfo {
	
	private	String	TaxCode	;	//	税费代码	
	private	String	TaxName	;	//	税费名称
	private	String	Currency	;	//	币种
	private	Double	TaxAmount	;	//	税费金额	SITI票的Q值换算为支付货币算法：逢一进十(ROE * NUC币种的Q值);SOTO票的Q值换算为支付货币算法：逢一进十((逢一进十(ROE * NUC币种的Q值))*RATE)
	private	String	TaxSuffix	;	//	税费后缀信息	
	private	int	TaxSeq	;	//	税费顺序号	
	
	
	public String getTaxCode() {
		return TaxCode;
	}
	public void setTaxCode(String taxCode) {
		TaxCode = taxCode;
	}

	public String getTaxName() {
		return TaxName;
	}

	public void setTaxName(String taxName) {
		TaxName = taxName;
	}

	public String getCurrency() {
		return Currency;
	}
	public void setCurrency(String currency) {
		Currency = currency;
	}
	public Double getTaxAmount() {
		return TaxAmount;
	}
	public void setTaxAmount(Double taxAmount) {
		TaxAmount = taxAmount;
	}
	public String getTaxSuffix() {
		return TaxSuffix;
	}
	public void setTaxSuffix(String taxSuffix) {
		TaxSuffix = taxSuffix;
	}
	public int getTaxSeq() {
		return TaxSeq;
	}
	public void setTaxSeq(int taxSeq) {
		TaxSeq = taxSeq;
	}
	@Override
	public String toString() {
		return "InternatTaxInfo [TaxCode=" + TaxCode + ", Currency=" + Currency
				+ ", TaxAmount=" + TaxAmount + ", TaxSuffix=" + TaxSuffix
				+ ", TaxSeq=" + TaxSeq + "]";
	}
	

}
