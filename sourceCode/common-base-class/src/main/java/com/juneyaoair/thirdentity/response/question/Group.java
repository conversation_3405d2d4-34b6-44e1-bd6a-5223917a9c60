package com.juneyaoair.thirdentity.response.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/4/9  10:21.
 * 分组
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Group {
    private Integer ID;//主键
    private String Title;//分组名称
    private String Description;//分组描述
    private String Remard;//备注
    private boolean IsEnable;//是否启用
    private boolean isAnswered;//是否已做  false 未作答 true已作答
    private String IconUrlOn;//分组图标(启用)
    private String IconUrlOff;//分组图标(禁用)
    private List<Squad> SquadEntitys;//小分组
    private List<Question> QuestionEntitys;//问卷对应问题
    private String iconUrl;
}
