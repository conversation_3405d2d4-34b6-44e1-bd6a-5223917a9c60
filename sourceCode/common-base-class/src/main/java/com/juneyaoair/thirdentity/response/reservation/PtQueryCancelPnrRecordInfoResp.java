package com.juneyaoair.thirdentity.response.reservation;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName PtQueryCancelPnrRecordInfoResp
 * <AUTHOR>
 * @Description
 * @Date 2021-08-03 16:52
 **/
@NoArgsConstructor
@XmlRootElement(name = "PtQueryCancelPnrRecordInfoResp")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PtQueryCancelPnrRecordInfoResp {
    private String Version; //版本号
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String ResultCode;  //结果代码
    private String ErrorInfo;   //错误信息


    /**
     * PageInfo : {"total":1,"list":[{"CancelOrderId":54,"TicketNo":"0181156013436","PName":"申梦月","Status":1,"CardNo":"******************","Pnr":"NB6PMK","FfCardNo":"2881717786","FfpId":3513951,"CancelDatetime":"2021-08-06 14:27:48"}],"pageNum":1,"pageSize":1,"size":1,"startRow":0,"endRow":0,"pages":1,"prePage":0,"nextPage":0,"isFirstPage":true,"isLastPage":true,"hasPreviousPage":false,"hasNextPage":false,"navigatePages":8,"navigatepageNums":[1],"navigateFirstPage":1,"navigateLastPage":1}
     */

    private PageInfoBean PageInfo;
}
