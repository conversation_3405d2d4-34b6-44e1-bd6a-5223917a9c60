package com.juneyaoair.thirdentity.response.reservation;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName PageInfoBean
 * <AUTHOR>
 * @Description
 * @Date 2021-08-06 15:09
 **/
@NoArgsConstructor
@Data
public class PageInfoBean {
    /**
     * total : 1
     * list : [{"CancelOrderId":54,"TicketNo":"0181156013436","PName":"申梦月","Status":1,"CardNo":"******************","Pnr":"NB6PMK","FfCardNo":"2881717786","FfpId":3513951,"CancelDatetime":"2021-08-06 14:27:48"}]
     * pageNum : 1
     * pageSize : 1
     * size : 1
     * startRow : 0
     * endRow : 0
     * pages : 1
     * prePage : 0
     * nextPage : 0
     * isFirstPage : true
     * isLastPage : true
     * hasPreviousPage : false
     * hasNextPage : false
     * navigatePages : 8
     * navigatepageNums : [1]
     * navigateFirstPage : 1
     * navigateLastPage : 1
     */

    private int total;
    private int pageNum;
    private int pageSize;
    private int size;
    private int startRow;
    private int endRow;
    private int pages;
    private int prePage;
    private int nextPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private boolean hasPreviousPage;
    private boolean hasNextPage;
    private int navigatePages;
    private int navigateFirstPage;
    private int navigateLastPage;
    private List<CancelOrderDTO> list;
    private List<Integer> navigatepageNums;
}
