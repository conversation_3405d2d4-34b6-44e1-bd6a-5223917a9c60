package com.juneyaoair.thirdentity.passengers.req;


import com.juneyaoair.thirdentity.request.booking.PtSegmentInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class
QueryPeersRequest {
  private String Version;

  private String ChannelCode;

  /**
   * 航班日期
   */
  private String FlightDate;

  /**
   * 票号
   */
  private String TicketNo;

  /**
   * FFPID
   */
  private String CustomerNo;

  private String PassengerName;

  private List<PtSegmentInfo> Segments;
}
