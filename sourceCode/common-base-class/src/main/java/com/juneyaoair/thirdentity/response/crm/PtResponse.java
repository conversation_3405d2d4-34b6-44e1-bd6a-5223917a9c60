package com.juneyaoair.thirdentity.response.crm;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/5/24  16:38.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtResponse {
    @SerializedName("StatusCode")
    private String statusCode; //接口返回码，”000”表示调用成功，”其他表示调用失败
    @SerializedName("Message")
    private String message;  //接口返回提示信息；成功返回成功
}
