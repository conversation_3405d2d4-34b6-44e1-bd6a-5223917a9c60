package com.juneyaoair.thirdentity.request.speedRefund;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 客票快退请求类
 */
@XmlRootElement(name = "TicketSpeedRefundDetailRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class TicketSpeedRefundDetailRequest {
    private String Version; // 版本号
    private String ChannelCode; // 渠道号 例如“WEIXIN”
    private String UserNo; //渠道工作人员号
    private String TicketPayoutNO; // 渠道退款编号 例如“POUT21062500000062”
    private String CustomerNo; //客户ID号


    public String getCustomerNo() {
        return CustomerNo;
    }

    public void setCustomerNo(String customerNo) {
        CustomerNo = customerNo;
    }

    public TicketSpeedRefundDetailRequest() {
    }

    public TicketSpeedRefundDetailRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getTicketPayoutNO() {
        return TicketPayoutNO;
    }

    public void setTicketPayoutNO(String ticketPayoutNO) {
        TicketPayoutNO = ticketPayoutNO;
    }
}
