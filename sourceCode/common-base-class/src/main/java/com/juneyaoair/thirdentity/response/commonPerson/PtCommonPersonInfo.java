package com.juneyaoair.thirdentity.response.commonPerson;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;

@XmlRootElement(name = "PtCommonPersonInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtCommonPersonInfo {

	private Integer CommonContactId; //常用旅客联系信息ID
	private String ChannelCustomerNo;//渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID，其它时为自编号
	private String ChannelCustomerType;//渠道客户类型,CRM - 常客,Other - 其它
	private String PassengerName; //乘客姓名	英文姓名可以根据/分出姓和名
	private String PassengerType; //乘客类型	ADT － 成人，CHD － 儿童，INF － 婴儿
	private String CertType; //证件类型	身份证:NI,护照:PP,其它证件:CC
	private String CertNo; //证件号码
	private String FfCardNo; //常客卡号	HO+卡号
	private String CountryTelCode; //手机号国际区号
	private String HandphoneNo; //手机号
	private String Birthdate; //出生日期yyyy-MM-dd	
	private String Sex; //性别	
	private String Nationality; //国籍	
	private String BelongCountry; //发证国
	private String CertValidity; //证件有效期yyyy-MM-dd	
	private String LastBookingTime; //最后预订时间yyyy-MM-dd HH:mm:ss	
	private String BookingNum; //预订次数	
	private String CreateDatetime; //机票订单创建时间格式:yyyy-MM-dd HH:mm:ss
	private String InterFlag; //国际国内标识
	private String SaCardNo;//星盟卡号 XX+卡号

	public String getSaCardNo() {
		return SaCardNo;
	}

	public void setSaCardNo(String saCardNo) {
		SaCardNo = saCardNo;
	}

	public Integer getCommonContactId() {
		return CommonContactId;
	}
	public void setCommonContactId(Integer commonContactId) {
		CommonContactId = commonContactId;
	}
	public String getPassengerName() {
		return PassengerName;
	}
	public void setPassengerName(String passengerName) {
		PassengerName = passengerName;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public String getCertType() {
		return CertType;
	}
	public void setCertType(String certType) {
		CertType = certType;
	}
	public String getCertNo() {
		return CertNo;
	}
	public void setCertNo(String certNo) {
		CertNo = certNo;
	}
	public String getFfCardNo() {
		return FfCardNo;
	}
	public void setFfCardNo(String ffCardNo) {
		FfCardNo = ffCardNo;
	}
	public String getCountryTelCode() {
		return CountryTelCode;
	}
	public void setCountryTelCode(String countryTelCode) {
		CountryTelCode = countryTelCode;
	}
	public String getHandphoneNo() {
		return HandphoneNo;
	}
	public void setHandphoneNo(String handphoneNo) {
		HandphoneNo = handphoneNo;
	}
	public String getBirthdate() {
		return Birthdate;
	}
	public void setBirthdate(String birthdate) {
		Birthdate = birthdate;
	}
	public String getSex() {
		return Sex;
	}
	public void setSex(String sex) {
		Sex = sex;
	}
	public String getNationality() {
		return Nationality;
	}
	public void setNationality(String nationality) {
		Nationality = nationality;
	}
	public String getBelongCountry() {
		return BelongCountry;
	}
	public void setBelongCountry(String belongCountry) {
		BelongCountry = belongCountry;
	}
	public String getCertValidity() {
		return CertValidity;
	}
	public void setCertValidity(String certValidity) {
		CertValidity = certValidity;
	}
	public String getLastBookingTime() {
		return LastBookingTime;
	}
	public void setLastBookingTime(String lastBookingTime) {
		LastBookingTime = lastBookingTime;
	}
	public String getBookingNum() {
		return BookingNum;
	}
	public void setBookingNum(String bookingNum) {
		BookingNum = bookingNum;
	}
	public String getCreateDatetime() {
		return CreateDatetime;
	}
	public void setCreateDatetime(String createDatetime) {
		CreateDatetime = createDatetime;
	}
	public String getChannelCustomerNo() {
		return ChannelCustomerNo;
	}
	public void setChannelCustomerNo(String channelCustomerNo) {
		ChannelCustomerNo = channelCustomerNo;
	}
	public String getChannelCustomerType() {
		return ChannelCustomerType;
	}
	public void setChannelCustomerType(String channelCustomerType) {
		ChannelCustomerType = channelCustomerType;
	}
	public String getInterFlag() {
		return InterFlag;
	}
	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}


	public String getParamString(){
		return this.InterFlag+this.PassengerType+this.PassengerName+this.CertType+this.CertNo+this.HandphoneNo;
	}
	
}