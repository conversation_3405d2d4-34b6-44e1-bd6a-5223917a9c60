package com.juneyaoair.thirdentity.response.order.apply;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "SegmentInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtSegmentInfo {
	private int SegmentID; //航段ID	保存在统一订单数据库中的ID
	private int SegNO; //旅行顺序	第一段为从0开始，第二段为1，依次增加
	private String FlightDirection; //飞行方向	去程为G,回程为B
	private String FlightNo; //航班号	
	private String DepDateTime; //航班起飞时间	yyyy-MM-dd HH:mm
	private String ArrDateTime; //航班到达时间	yyyy-MM-dd HH:mm
	private String DepCity; //起飞城市三字码	
	private String ArrCity; //到达城市三字码	
	private String DepAirport; //起飞机场三字码	
	private String ArrAirport; //到达机场三字码	
	private String Cabin; //舱位	
	private String CabinClass; //舱位等级	
	private boolean IsCodeShare	; //是否共享航班	
	private String CarrierFlightNo; //承运航班号	
	private String MealCode; //餐食代码	
	private boolean IsSeatedOnPlane; //是否可以机上订位	
	private String PlaneStyle; //机型	
	private String DepTerm; //起飞航站楼	
	private String ArrTerm; //到达航站楼	
	private int StopNumber; //经停次数	
	private String TicketLaunchDatetime; //出票时的起飞时间	yyyy-MM-dd HH:mm
	public int getSegmentID() {
		return SegmentID;
	}
	public void setSegmentID(int segmentID) {
		SegmentID = segmentID;
	}
	public int getSegNO() {
		return SegNO;
	}
	public void setSegNO(int segNO) {
		SegNO = segNO;
	}
	public String getFlightDirection() {
		return FlightDirection;
	}
	public void setFlightDirection(String flightDirection) {
		FlightDirection = flightDirection;
	}
	public String getFlightNo() {
		return FlightNo;
	}
	public void setFlightNo(String flightNo) {
		FlightNo = flightNo;
	}
	public String getDepDateTime() {
		return DepDateTime;
	}
	public void setDepDateTime(String depDateTime) {
		DepDateTime = depDateTime;
	}
	public String getArrDateTime() {
		return ArrDateTime;
	}
	public void setArrDateTime(String arrDateTime) {
		ArrDateTime = arrDateTime;
	}
	public String getDepCity() {
		return DepCity;
	}
	public void setDepCity(String depCity) {
		DepCity = depCity;
	}
	public String getArrCity() {
		return ArrCity;
	}
	public void setArrCity(String arrCity) {
		ArrCity = arrCity;
	}
	public String getDepAirport() {
		return DepAirport;
	}
	public void setDepAirport(String depAirport) {
		DepAirport = depAirport;
	}
	public String getArrAirport() {
		return ArrAirport;
	}
	public void setArrAirport(String arrAirport) {
		ArrAirport = arrAirport;
	}
	public String getCabin() {
		return Cabin;
	}
	public void setCabin(String cabin) {
		Cabin = cabin;
	}
	public String getCabinClass() {
		return CabinClass;
	}
	public void setCabinClass(String cabinClass) {
		CabinClass = cabinClass;
	}
	public boolean isIsCodeShare() {
		return IsCodeShare;
	}
	public void setIsCodeShare(boolean isCodeShare) {
		IsCodeShare = isCodeShare;
	}
	public String getCarrierFlightNo() {
		return CarrierFlightNo;
	}
	public void setCarrierFlightNo(String carrierFlightNo) {
		CarrierFlightNo = carrierFlightNo;
	}
	public String getMealCode() {
		return MealCode;
	}
	public void setMealCode(String mealCode) {
		MealCode = mealCode;
	}
	public boolean isIsSeatedOnPlane() {
		return IsSeatedOnPlane;
	}
	public void setIsSeatedOnPlane(boolean isSeatedOnPlane) {
		IsSeatedOnPlane = isSeatedOnPlane;
	}
	public String getPlaneStyle() {
		return PlaneStyle;
	}
	public void setPlaneStyle(String planeStyle) {
		PlaneStyle = planeStyle;
	}
	public String getDepTerm() {
		return DepTerm;
	}
	public void setDepTerm(String depTerm) {
		DepTerm = depTerm;
	}
	public String getArrTerm() {
		return ArrTerm;
	}
	public void setArrTerm(String arrTerm) {
		ArrTerm = arrTerm;
	}
	public int getStopNumber() {
		return StopNumber;
	}
	public void setStopNumber(int stopNumber) {
		StopNumber = stopNumber;
	}
	public String getTicketLaunchDatetime() {
		return TicketLaunchDatetime;
	}
	public void setTicketLaunchDatetime(String ticketLaunchDatetime) {
		TicketLaunchDatetime = ticketLaunchDatetime;
	}
}