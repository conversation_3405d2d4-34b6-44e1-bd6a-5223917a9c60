package com.juneyaoair.thirdentity.request.speedRefund;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/25  15:34.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtTicketPayoutRetryRequest {
    private String Version;  //接口版本号
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String ChannelPayoutNo;  //渠道退款编号
    private String TicketPayoutNo;  //客票退款编号
    private String ChannelCustomerNo;  //渠道客户编号
    private String BankAccountNo;  //银行账户
    private String BankName;  //银行名称
    private String HandphoneNo;    //手机号

    public PtTicketPayoutRetryRequest(String version,String channelCode,String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
    }
}
