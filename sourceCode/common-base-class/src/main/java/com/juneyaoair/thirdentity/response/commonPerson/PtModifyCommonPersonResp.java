package com.juneyaoair.thirdentity.response.commonPerson;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PtModifyCommonPersonResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtModifyCommonPersonResp {
	private String Version; //接口版本号10
	private String ChannelCode; //渠道用户号B2C,CC等
	private String UserNo; //渠道工作人员号分配给渠道用户的工作人员号
	private String ChannelCustomerNo; // 渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID
	private String ChannelCustomerType; // 渠道客户类型,CRM - 常客,Other - 其它
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息

	public String getVersion(){
		return Version;
	}
	public void setVersion(String Version){
		this.Version=Version;
	}
	public String getChannelCode(){
		return ChannelCode;
	}
	public void setChannelCode(String ChannelCode){
		this.ChannelCode=ChannelCode;
	}
	public String getUserNo(){
		return UserNo;
	}
	public void setUserNo(String UserNo){
		this.UserNo=UserNo;
	}
	public String getChannelCustomerType() {
		return ChannelCustomerType;
	}
	public void setChannelCustomerType(String channelCustomerType) {
		ChannelCustomerType = channelCustomerType;
	}

	public String getChannelCustomerNo() {
		return ChannelCustomerNo;
	}
	public void setChannelCustomerNo(String channelCustomerNo) {
		ChannelCustomerNo = channelCustomerNo;
	}
	public String getResultCode(){
		return ResultCode;
	}
	public void setResultCode(String ResultCode){
		this.ResultCode=ResultCode;
	}
	public String getErrorInfo(){
		return ErrorInfo;
	}
	public void setErrorInfo(String ErrorInfo){
		this.ErrorInfo=ErrorInfo;
	}

}