package com.juneyaoair.thirdentity.upclass.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/14  16:25.
 */
@Data
public class PtTicketUpgradeFeeRequest {
    /**
     * 接口版本号
     */
    private String Version;
    /**
     * 渠道用户号
     */
    private String ChannelCode;
    /**
     * 渠道工作人员号
     */
    private String UserNo;
    /**
     * 随机码  用于业务追踪
     */
    private String RandCode;
    /**
     * 航程类型  单程：OW；往返：RT（RT时请注意飞行方向）
     */
    private String RouteType;
    /**
     * 币种代码  CNY人民币
     */
    private String CurrencyCode;
    /**
     * 语言代码  如：CN中文 EN英文 TH泰文 KR韩文 JP日文 HK中文繁体
     */
    private String LangCode;
    /**
     * 国内国际标志
     */
    private String InterFlag;

    private List<PtTicketUpgradeFeeInfo> TicketUpgradeFeeInfo;

    public PtTicketUpgradeFeeRequest(String version,String channelCode,String userNo,String currencyCode,String langCode){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.CurrencyCode = currencyCode;
        this.LangCode = langCode;
    }
}
