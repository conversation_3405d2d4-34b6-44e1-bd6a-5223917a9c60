package com.juneyaoair.thirdentity.response.activity;

import com.juneyaoair.thirdentity.request.activity.ActivityAirline;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by zc on 2016/10/28.
 */
@XmlRootElement(name = "QueryActivityAirlineResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryActivityAirlineResponse {

    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<ActivityAirline> ActivityAirlineList;

    public QueryActivityAirlineResponse() {
        super();
    }

    public QueryActivityAirlineResponse(String version, String channelCode) {
        super();
        Version = version;
        ChannelCode = channelCode;
    }

    public String getVersion() {
        return Version;
    }
    public void setVersion(String version) {
        Version = version;
    }
    public String getChannelCode() {
        return ChannelCode;
    }
    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public List<ActivityAirline> getActivityAirlineList() {
        return ActivityAirlineList;
    }

    public void setActivityAirlineList(List<ActivityAirline> activityAirlineList) {
        ActivityAirlineList = activityAirlineList;
    }
}
