package com.juneyaoair.thirdentity.tongdun;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/4/26 14:23
 */
public enum FinalDecisionEnum {
    ACCEPT("Accept","1"),
    REVIEW("Review","2"),
    REJECT("Reject","3");
    private  String code;
    private  String name;
    FinalDecisionEnum(String code,String name){
        this.code = code;
        this.name = name;
    }
    public static FinalDecisionEnum checkEnum(String v){
        for (FinalDecisionEnum c: FinalDecisionEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
