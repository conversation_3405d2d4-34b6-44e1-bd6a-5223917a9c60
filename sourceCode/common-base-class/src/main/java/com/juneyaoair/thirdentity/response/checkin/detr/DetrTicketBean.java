package com.juneyaoair.thirdentity.response.checkin.detr;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "DetrTicketBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class DetrTicketBean {
	private String passengerName;
	private String tktNumber;
	private String isReceiptPrinted;
	private List<DetrTourBean> tours;
	
	public String getPassengerName() {
		return passengerName;
	}
	public void setPassengerName(String passengerName) {
		this.passengerName = passengerName;
	}	
	public String getTktNumber() {
		return tktNumber;
	}
	public void setTktNumber(String tktNumber) {
		this.tktNumber = tktNumber;
	}
	public String getIsReceiptPrinted() {
		return isReceiptPrinted;
	}
	public void setIsReceiptPrinted(String isReceiptPrinted) {
		this.isReceiptPrinted = isReceiptPrinted;
	}
	public List<DetrTourBean> getTours() {
		return tours;
	}
	public void setTours(List<DetrTourBean> tours) {
		this.tours = tours;
	}
	
	
	
	
	
	
}
