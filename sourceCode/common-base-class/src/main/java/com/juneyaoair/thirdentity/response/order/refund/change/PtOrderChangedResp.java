package com.juneyaoair.thirdentity.response.order.refund.change;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 
 * 根据订单编号，查询是否有升舱或改期订单没有退
 * <AUTHOR>
 * @date 2018-11-29 16:23:26
 */
@XmlRootElement(name = "OrderChangedResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtOrderChangedResp{
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	private String Version; //接口版本号10
	private String ChannelCode; //渠道用户号B2C,CC等
	private String UserNo; //渠道用户人员号分配给渠道用户的工作人员号
	
	private String OrderNo; //订单编号
    private String ChannelOrderNo; //渠道订单编号
    private Boolean HasChangeRefund = false;//是否有升仓订单没有退
	
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getOrderNo() {
		return OrderNo;
	}
	public void setOrderNo(String orderNo) {
		OrderNo = orderNo;
	}
	public String getChannelOrderNo() {
		return ChannelOrderNo;
	}
	public void setChannelOrderNo(String channelOrderNo) {
		ChannelOrderNo = channelOrderNo;
	}
	public Boolean getHasChangeRefund() {
		return HasChangeRefund;
	}
	public void setHasChangeRefund(Boolean hasChangeRefund) {
		HasChangeRefund = hasChangeRefund;
	}
}
