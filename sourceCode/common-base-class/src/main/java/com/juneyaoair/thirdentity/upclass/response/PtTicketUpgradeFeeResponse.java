package com.juneyaoair.thirdentity.upclass.response;


import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/14  18:29.
 */
@Data
public class PtTicketUpgradeFeeResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    /**
     *航段航班信息列表
     */
    @SerializedName("FlightInfoList")
    private List<UpAVFlightInfo> flightInfoList;
    private String ResultCode;
    private String ErrorInfo;
}

