package com.juneyaoair.thirdentity.response.av;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlRootElement(name = "QueryFareResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"Version","ChannelCode","UserNo","RouteType","CurrencyCode","TicketingDate",
		"PriceProductTypeList","Direct","InterFlag","SegmentList","ResultCode","ErrorInfo"})
//统一订单平台返回结果
public class QueryFareResponse {
	
	private	String	Version;	//	接口版本号	10
	private	String	ChannelCode;	//	渠道用户号	B2C,CC等
	private	String	UserNo;	//	渠道工作人员号	分配给渠道用户的工作人员号
	private	String	RouteType;	//	航程类型	单程：OW；往返：RT
	private	String	CurrencyCode;	//	币种代码	CNY人民币
	private	String	TicketingDate;	//	出票时间，可以理解为：航班预订时间，即当前时间。	yyyy-MM-dd hh:mi
	private	String[]	PriceProductTypeList;	//	运价产品类型	1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程,为空时表示查询所有,只需要查询某项或某几项时给出需要的产品代码
	private	String	Direct;	//	中转方案	"D － 只查询直达航班和运价，T － 没有直达方案时，给出中转方案运价，S － 没有直达方案时,只给出中转航程，不查询航班可利用座位和运价"
	private	String	InterFlag;	//	国际国内标识	D － 国内，I － 国际
	private	Segment[]	SegmentList;	//	航段运价信息	
	private	String	ResultCode;	//	结果代码	1001 － 成功，其它失败
	private	String	ErrorInfo;	//	错误信息
	
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getRouteType() {
		return RouteType;
	}
	public void setRouteType(String routeType) {
		RouteType = routeType;
	}
	public String getCurrencyCode() {
		return CurrencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		CurrencyCode = currencyCode;
	}
	public String getTicketingDate() {
		return TicketingDate;
	}
	public void setTicketingDate(String ticketingDate) {
		TicketingDate = ticketingDate;
	}
	public String[] getPriceProductTypeList() {
		return PriceProductTypeList;
	}
	public void setPriceProductTypeList(String[] priceProductTypeList) {
		PriceProductTypeList = priceProductTypeList;
	}
	public String getDirect() {
		return Direct;
	}
	public void setDirect(String direct) {
		Direct = direct;
	}
	public String getInterFlag() {
		return InterFlag;
	}
	public void setInterFlag(String interFlag) {
		InterFlag = interFlag;
	}
	public Segment[] getSegmentList() {
		return SegmentList;
	}
	public void setSegmentList(Segment[] segmentList) {
		SegmentList = segmentList;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}
	
	@Override
	public String toString() {
		return "QueryFareResponse [Version=" + Version + ", ChannelCode="
				+ ChannelCode + ", UserNo=" + UserNo + ", RouteType="
				+ RouteType + ", CurrencyCode=" + CurrencyCode
				+ ", TicketingDate=" + TicketingDate
				+ ", PriceProductTypeList="
				+ Arrays.toString(PriceProductTypeList) + ", Direct=" + Direct
				+ ", InterFlag=" + InterFlag + ", SegmentList="
				+ Arrays.toString(SegmentList) + ", ResultCode=" + ResultCode
				+ ", ErrorInfo=" + ErrorInfo + "]";
	}
	
	
	

}
