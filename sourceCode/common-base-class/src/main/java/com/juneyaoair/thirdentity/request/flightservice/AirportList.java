package com.juneyaoair.thirdentity.request.flightservice;

import lombok.Data;

/**
 * Created by guan<PERSON>yin on 2018/12/5.
 */
@Data
public class AirportList {
    private String CityName;//城市名
    private String AirportCode;//机场三字码
    private String AirportName;//机场名
    private String AirportEnName;//机场名拼音
    private String Website;
    private String Terminalposition;//航站位置
    private String Checkincounter;//值机柜台
    private String Firstclasscheckincounter;//头等舱/金卡/白金卡 值机柜台
    private String Ticketcounter;//机场售票柜台
    private String Checkinbegintime;//值机开放时间
    private String Checkinendtime;//值机关闭时间
    private String Viproom;//贵宾室位置
    private String TerminalpositionI;
    private String CheckincounterI;
    private String FirstclasscheckincounterI;
    private String TicketcounterI;
    private String CheckinbegintimeI;
    private String ViproomI;


}
