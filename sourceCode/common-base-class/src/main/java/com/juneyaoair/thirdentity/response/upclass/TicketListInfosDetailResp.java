package com.juneyaoair.thirdentity.response.upclass;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:44 2018/9/28
 * @Modified by:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TicketListInfosDetailResp {
    private String ResultCode;//结果代码 1001 － 成功，其它失败
    private String ErrorInfo;//错误信息
    private String ChannelCode;//渠道用户号
    private String ChannelOrderNo;//渠道订单编号
    private String UserNo;//渠道工作人员号
    private List<IBETicketInfo> IBETicketInfoList;//航段信息列表

}
