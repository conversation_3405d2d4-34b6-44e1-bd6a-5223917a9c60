package com.juneyaoair.thirdentity.response.checkin;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "CkiInfoQueryBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class CkiInfoQueryBean {
	private  long tipCode ;
	private String  messageCause ;
	private List<CkiInfo>  checkInList;

	public List<CkiInfo> getCheckInList() {
		return checkInList;
	}

	public void setCheckInList(List<CkiInfo> checkInList) {
		this.checkInList = checkInList;
	}

	public long getTipCode() {
		return tipCode;
	}

	public void setTipCode(long tipCode) {
		this.tipCode = tipCode;
	}

	public String getMessageCause() {
		return messageCause;
	}

	public void setMessageCause(String messageCause) {
		this.messageCause = messageCause;
	}
}
