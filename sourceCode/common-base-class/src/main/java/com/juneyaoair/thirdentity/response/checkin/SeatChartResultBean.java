package com.juneyaoair.thirdentity.response.checkin;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "SeatChartResultBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class SeatChartResultBean {
	private String seatMap;
	private String planeType;
	private List<SeatChart> seatMaplist;
	public String getSeatMap() {
		return seatMap;
	}
	public void setSeatMap(String seatMap) {
		this.seatMap = seatMap;
	}
	public String getPlaneType() {
		return planeType;
	}
	public void setPlaneType(String planeType) {
		this.planeType = planeType;
	}
	public List<SeatChart> getSeatMaplist() {
		return seatMaplist;
	}
	public void setSeatMaplist(List<SeatChart> seatMaplist) {
		this.seatMaplist = seatMaplist;
	}

}
