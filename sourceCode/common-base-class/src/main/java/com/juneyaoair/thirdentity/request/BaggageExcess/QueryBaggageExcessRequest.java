package com.juneyaoair.thirdentity.request.BaggageExcess;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 9:57 2018/1/7
 * @Modified by:
 */
@XmlRootElement(name = "QueryBaggageExcessReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryBaggageExcessRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private List<QueryBaggageExcessSegmentInfo> SegmentInfoList;

    public QueryBaggageExcessRequest() {
    }

    public QueryBaggageExcessRequest(String version, String channelCode, String userNo, List<QueryBaggageExcessSegmentInfo> segmentInfoList) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        SegmentInfoList = segmentInfoList;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public List<QueryBaggageExcessSegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<QueryBaggageExcessSegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }
}
