package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PsrCheckInResult")
@XmlAccessorType(XmlAccessType.FIELD)
public class PsrCheckInResult {
	private String tipCode ;
	private String messageCause ;
	
	private String flightNo;
	private String flightDate;
	private String psrCnName;
	private String deptAirport;
	private String boardStream;
	private String cabin;
	private String seatNo;
	private String boardingGateNumber;
	
	public String getFlightNo() {
		return flightNo;
	}
	public void setFlightNo(String flightNo) {
		this.flightNo = flightNo;
	}
	public String getFlightDate() {
		return flightDate;
	}
	public void setFlightDate(String flightDate) {
		this.flightDate = flightDate;
	}
	
	public String getDeptAirport() {
		return deptAirport;
	}
	public void setDeptAirport(String deptAirport) {
		this.deptAirport = deptAirport;
	}
	public String getBoardStream() {
		return boardStream;
	}
	public void setBoardStream(String boardStream) {
		this.boardStream = boardStream;
	}
	public String getCabin() {
		return cabin;
	}
	public void setCabin(String cabin) {
		this.cabin = cabin;
	}
	public String getSeatNo() {
		return seatNo;
	}
	public void setSeatNo(String seatNo) {
		this.seatNo = seatNo;
	}
	public String getTipCode() {
		return tipCode;
	}
	public void setTipCode(String tipCode) {
		this.tipCode = tipCode;
	}
	public String getMessageCause() {
		return messageCause;
	}
	public void setMessageCause(String messageCause) {
		this.messageCause = messageCause;
	}
	public String getPsrCnName() {
		return psrCnName;
	}
	public void setPsrCnName(String psrCnName) {
		this.psrCnName = psrCnName;
	}
	public String getBoardingGateNumber() {
		return boardingGateNumber;
	}
	public void setBoardingGateNumber(String boardingGateNumber) {
		this.boardingGateNumber = boardingGateNumber;
	}

	
	
}
