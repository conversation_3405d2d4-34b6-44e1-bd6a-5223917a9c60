package com.juneyaoair.thirdentity.passengers.req;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.request.PtBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 新增，修改证件信息
 * @date 2019/8/7  16:38.
 */
@Data
public class GeneralCert extends PtBaseRequest {
    @SerializedName("ChannelCustomerNo")
    private String channelCustomerNo;
    @SerializedName("ChannelCustomerType")
    private String channelCustomerType;
    @SerializedName("GeneralContactCertId")
    private Integer generalContactCertId;//常用乘机人证件信息ID
    @SerializedName("GeneralContactId")
    private Integer generalContactId;//常用乘机人信息ID
    @SerializedName("CertType")
    private String certType;
    @SerializedName("CertNo")
    private String certNo;
    @SerializedName("BelongCountry")
    private String belongCountry;
    @SerializedName("CertValidity")
    private String certValidity;
    @SerializedName("IsRemove")
    private Boolean isRemove;

    public GeneralCert(String version,String channelCode,String userNo){
        super(version,channelCode,userNo);
    }
}
